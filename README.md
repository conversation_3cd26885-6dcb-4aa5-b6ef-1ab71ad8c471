# ca<PERSON><PERSON><PERSON><PERSON>'s miscellaneous notes

Blog cá nhân được xây dựng bằng Hugo và theme Archie, với chức năng tìm kiếm được tích hợp sẵn.

## Tính năng

- ✅ Responsive design
- ✅ Dark/Light mode toggle
- ✅ Hỗ trợ KaTeX cho công thức toán học
- ✅ **Chức năng tìm kiếm toàn văn** với Pagefind
- ✅ Tags và categories
- ✅ RSS feed
- ✅ Tự động deploy với GitHub Actions

## Chức năng tìm kiếm

Website đã được tích hợp chức năng tìm kiếm mạnh mẽ sử dụng [Pagefind](https://pagefind.app/):

- **Tìm kiếm trong bài viết**: Chỉ tìm kiếm trong các bài post, không index các trang khác
- **Tìm kiếm nhanh**: Kết quả hiển thị ngay khi gõ
- **Hỗ trợ tiếng Việt**: Interface và kết quả tìm kiếm bằng tiếng Việt
- **Responsive**: Hoạt động tốt trên mọi thiết bị

### Cách sử dụng

1. Truy cập trang [Search](/search) từ menu điều hướng
2. Nhập từ khóa cần tìm vào ô tìm kiếm
3. Kết quả sẽ hiển thị ngay lập tức với:
   - Tiêu đề bài viết
   - Đoạn trích có chứa từ khóa
   - Link trực tiếp đến bài viết

## Cấu trúc thư mục

```
├── content/
│   ├── posts/          # Các bài viết blog
│   └── search.md       # Trang tìm kiếm
├── layouts/
│   └── _default/
│       └── search.html # Template cho trang tìm kiếm
├── static/             # File tĩnh
├── themes/archie/      # Theme Archie
└── .github/workflows/  # GitHub Actions cho deployment
```

## Development

### Yêu cầu

- Hugo Extended (>= 0.145.0)
- Python 3.x (để chạy Pagefind)

### Chạy local

```bash
# Clone repository
git clone https://github.com/caubechankiu/caubechankiu.github.io.git
cd caubechankiu.github.io

# Cài đặt submodules (theme)
git submodule update --init --recursive

# Build site
hugo --minify

# Cài đặt và chạy Pagefind để tạo search index (chỉ index các bài post)
pip install pagefind-bin
python -m pagefind_bin --site public --glob "posts/**/*.html"

# Chạy development server
hugo server
```

Website sẽ có sẵn tại `http://localhost:1313`

### Thêm bài viết mới

```bash
# Tạo bài viết mới
hugo new posts/ten-bai-viet/index.md
```

Hoặc tạo thủ công trong `content/posts/` với format:

```yaml
---
title: "Tiêu đề bài viết"
date: 2024-01-01T00:00:00+07:00
tags: ["tag1", "tag2"]
description: "Mô tả ngắn"
---

Nội dung bài viết...
```

## Deployment

Website được tự động deploy lên GitHub Pages khi có commit mới vào branch `main`. 

Quy trình deployment:
1. Build Hugo site
2. Cài đặt Pagefind
3. Tạo search index
4. Deploy lên GitHub Pages

## License

MIT License - xem file [LICENSE](LICENSE) để biết thêm chi tiết.
