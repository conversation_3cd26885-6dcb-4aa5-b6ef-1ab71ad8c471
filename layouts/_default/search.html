{{ define "main" }}
<main>
    <div class="search-container">
        <h1>Search</h1>
        <div id="search"></div>
    </div>
</main>

<link href="/pagefind/pagefind-ui.css" rel="stylesheet">
<script src="/pagefind/pagefind-ui.js"></script>

<style>
    .search-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem 1rem;
    }

    .search-container h1 {
        margin-bottom: 2rem;
        text-align: center;
    }

    /* Custom styling for Pagefind UI */
    #search {
        margin-top: 1rem;
    }

    .pagefind-ui__search-input {
        width: 100%;
        padding: 12px 16px;
        font-size: 16px;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        background: var(--bg);
        color: var(--fg);
        transition: border-color 0.2s ease;
    }

    .pagefind-ui__search-input:focus {
        outline: none;
        border-color: #007acc;
    }

    .pagefind-ui__result {
        margin: 1rem 0;
        padding: 1rem;
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        background: var(--bg);
    }

    .pagefind-ui__result-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .pagefind-ui__result-title a {
        color: var(--fg);
        text-decoration: none;
    }

    .pagefind-ui__result-title a:hover {
        text-decoration: underline;
    }

    .pagefind-ui__result-excerpt {
        color: var(--light-fg);
        line-height: 1.5;
    }

    .pagefind-ui__result-tags {
        margin-top: 0.5rem;
    }

    .pagefind-ui__result-tag {
        display: inline-block;
        padding: 0.2rem 0.5rem;
        margin-right: 0.5rem;
        background: var(--accent);
        color: var(--bg);
        border-radius: 4px;
        font-size: 0.8rem;
    }

    /* Dark mode adjustments */
    @media (prefers-color-scheme: dark) {
        .pagefind-ui__search-input {
            border-color: #444;
            background: var(--bg);
            color: var(--fg);
        }

        .pagefind-ui__result {
            border-color: #444;
            background: var(--bg);
        }
    }
</style>

<script>
    window.addEventListener('DOMContentLoaded', (event) => {
        new PagefindUI({
            element: "#search",
            showSubResults: true,
            showImages: false,
            excerptLength: 200,
            resetStyles: false,
            translations: {
            }
        });
    });
</script>
{{ end }}