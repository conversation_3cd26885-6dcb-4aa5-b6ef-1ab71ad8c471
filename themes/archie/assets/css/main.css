/* Markdown */
:root{
--maincolor: red;
--bordercl:rebeccapurple;
--callouctcolor:dodgerblue;
--hovercolor:navy;
--darkMaincolor: #50fa7b;
}
html {
  color: #232333;
  font-family: 'Roboto Mono', monospace;
  font-size: 15px;
  line-height: 1.6em;
}
body{
  display: block;
  margin: 8px;
}
* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

::selection {
  background: var(--maincolor);
  color: #fff;
}

p {
  font-family: 'Fira Sans', sans-serif;
  line-height: 1.5;
}

hr {
  border: 0;
  border-top: 3px dotted var(--bordercl);
  margin: 1em 0;
}

blockquote {
  border-left: 3px solid var(--bordercl);
  color: #737373;
  margin: 0;
  padding-left: 1em;
}

a {
  border-bottom: 3px solid var(--maincolor);
  color: inherit;
  text-decoration: none;
}
a:hover {
    background-color: var(--hovercolor);
    color: #fff;
}

ul {
  list-style: none;
  padding-left: 2ch;
}
ul li {
  text-indent: -2ch;
}
ul > li::before {
  content: '* ';
  font-weight: bold;
}

/* Images */
img {
  border: 3px solid #ececec;
  max-width: 100%;
}

figure {
  box-sizing: border-box;
  display: inline-block;
  margin: 0;
  max-width: 100%;
}

figure img {
  max-height: 500px;
}

@media screen and (min-width: 600px) {
  figure {
    padding: 0 40px;
  }
}

figure h4 {
  font-size: 1rem;
  margin: 0;
  margin-bottom: 1em;
}
figure h4::before {
  content: '↳ ';
}

/* Code blocks */
code {
  background-color: #f1f1f1;
  padding: .1em .2em;
}

pre {
  background-color: #ececec;
  line-height: 1.4;
  overflow-x: auto;
  padding: 1em;
}

.highlight pre ::selection {
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
}

pre code {
  background-color: transparent;
  color: inherit;
  font-size: 100%;
  padding: 0;
}

/* Containers */
.content {
  margin-bottom: 4em;
  margin-left: auto;
  margin-right: auto;
  max-width: 800px;
  padding: 0 1ch;
  word-wrap: break-word;
}

/* Header */
header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: 1em 0;
  line-height: 2.5em;
}

header .main {
  font-size: 1.5rem;
}
h1, h2, h3, h4, h5, h6 {
  font-size: 1.2rem;
  margin-top: 2em;
}

h1::before { color: var(--maincolor); content: '# '; }
h2::before { color: var(--maincolor); content: '## '; }
h3::before { color: var(--maincolor); content: '### '; }
h4::before { color: var(--maincolor); content: '#### '; }
h5::before { color: var(--maincolor); content: '##### '; }
h6::before { color: var(--maincolor); content: '###### '; }

.meta {
  color: #999;
  letter-spacing: -0.5px;
}

.post-container {
  align-items: flex-start;
  display: flex;
  gap: 20px;
}

.post-content {
  flex: 3;
  min-width: 0;
}

.toc {
  background-color: #ececec;
  border-radius: 5px;
  color: #232333;
  flex: 0 0 auto;
  height: auto;
  margin-left: 20px;
  max-width: 300px;
  overflow-y: auto;
  padding: 10px;
  position: sticky;
  top: 20px;
}

/* Footer */
footer {
  display: flex;
  align-items: center;
  border-top: 0.4rem dotted var(--bordercl);
  padding: 2rem 0rem;
  margin-top: 2rem;
}
.soc {
  display: flex;
  align-items: center;
  border-bottom: none;
}
.border {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  border: 1px solid;
}
.footer-info {
  padding: var(--footer-padding);
}

/* Common */
.title h1 {
  margin-bottom: 0;
}

time {
  color: grey;
}

/* Posts */
article .title {
  margin-bottom: 1em;
}


/* Callout */
.callout {
  background-color: var(--callouctcolor);
  color: #fff;
  padding: 1em;
}

.callout p {
  font-family: 'IBM Plex Mono', monospace;
  margin: 0;
}

.callout a {
  border-bottom: 3px solid #fff;
}

.callout a:hover {
  background-color: #fff;
  color: var(--callouctcolor);
}

.callout-alert {
  color: #000000;
  background-color: transparent;
  border-width: 3px;
  border-style: solid;
  border-color: #ff6347;
}

.callout-custom {
  color: #000000;
}

.callout-tip {
  color: #000000;
  background-color: transparent;
  border-width: 3px;
  border-style: solid;
  border-color: dodgerblue;
}

.callout-warning {
  color: #000000;
  background-color: transparent;
  border-width: 3px;
  border-style: solid;
  border-color: #ffd700;
}

.site-description {
display: flex;
justify-content: space-between;
}
.tags li::before{
  content: "🏷 ";
}
.tags a{
  border-bottom: 3px solid var(--maincolor); 
}
.tags a:hover{
  color:white;
  background-color: var(--hovercolor); 
}
svg{
  max-height: 15px;
}
.soc:hover{
  color: white;
}
.draft-label{ 
    color: var(--bordercl);
    text-decoration: none;
    padding: 2px 4px;
    border-radius: 4px;
    margin-left: 6px;
    background-color: #f9f2f4;
}
.highlight {
  position: relative;
  -webkit-overflow-scrolling: touch;
}
.highlight pre code[class*="language-"] {
  -webkit-overflow-scrolling: touch;
}
.highlight pre code[class*="language-"]::before {
  background: black;
  border-radius: 0 0 0.25rem 0.25rem;
  color: white;
  font-size: 12px;
  letter-spacing: 0.025rem;
  padding: 0.1rem 0.5rem;
  position: absolute;
  right: 1rem;
  text-align: right;
  text-transform: uppercase;
  top: 0;
}

.highlight pre code[class=language-javaScript]::before,
.highlight pre code[class="language-js"]::before {
content: "js";
background: #f7df1e;
color: black;
}
.highlight pre code[class*='language-yml']::before,
.highlight pre code[class*='language-yaml']::before {
content: 'yaml';
background: #f71e6a;
color: white;
}
.highlight pre code[class*='language-shell']::before,
.highlight pre code[class*='language-bash']::before,
.highlight pre code[class*='language-sh']::before {
content: 'shell';
background: green;
color:white
}
.highlight pre code[class*='language-json']::before{
content: 'json';
background: dodgerblue;
 color: #000000 
}
.highlight pre code[class*='language-python']::before,
.highlight pre code[class*='language-py']::before {
content: 'py';
background: blue;
color: yellow ;
}
.highlight pre code[class*='language-css']::before{
content: 'css';
background: cyan;
color: black ;
}
.highlight pre code[class*='language-go']::before{
content: 'Go';
background: cyan;
color: royalblue ;
}
.highlight pre code[class*='language-md']::before,
.highlight pre code[class*='language-md']::before{
content: 'Markdown';
background: royalblue;
color: whitesmoke ;
}
.highlight pre code[class*='language-ts']::before{
  content: 'TS';
  background: #3178C6;
  color: #fff;
}

/* table */
table {
  border-spacing: 0;
  border-collapse: collapse;
}

table th{
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
  font-size: large;
}

table td{
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.feather {
  width: 24px;
  height: 24px;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}
