@font-face {
  font-family: 'Simple Icons';
  src: url(/fonts/SimpleIcons.woff2) format('woff2');
}

.si {
  font-style: normal;
  font-family: 'Simple Icons', sans-serif;
  vertical-align: middle;
}

.si-42::before { content: "\ea09"; }
.si-42.si--color::before { color: #000000; }
.si-1001tracklists::before { content: "\ea01"; }
.si-1001tracklists.si--color::before { color: #40AEF0; }
.si-1dot1dot1dot1::before { content: "\ea02"; }
.si-1dot1dot1dot1.si--color::before { color: #221E68; }
.si-1panel::before { content: "\ea03"; }
.si-1panel.si--color::before { color: #0854C1; }
.si-1password::before { content: "\ea04"; }
.si-1password.si--color::before { color: #3B66BC; }
.si-2fas::before { content: "\ea05"; }
.si-2fas.si--color::before { color: #EC1C24; }
.si-2k::before { content: "\ea06"; }
.si-2k.si--color::before { color: #DD0700; }
.si-365datascience::before { content: "\ea07"; }
.si-365datascience.si--color::before { color: #000C1F; }
.si-3m::before { content: "\ea08"; }
.si-3m.si--color::before { color: #FF0000; }
.si-4chan::before { content: "\ea0a"; }
.si-4chan.si--color::before { color: #006600; }
.si-4d::before { content: "\ea0b"; }
.si-4d.si--color::before { color: #004088; }
.si-500px::before { content: "\ea0c"; }
.si-500px.si--color::before { color: #222222; }
.si-7zip::before { content: "\ea0d"; }
.si-7zip.si--color::before { color: #000000; }
.si-99designs::before { content: "\ea0e"; }
.si-99designs.si--color::before { color: #FE5F50; }
.si-9gag::before { content: "\ea0f"; }
.si-9gag.si--color::before { color: #000000; }
.si-abbott::before { content: "\ea10"; }
.si-abbott.si--color::before { color: #008FC7; }
.si-abbrobotstudio::before { content: "\ea11"; }
.si-abbrobotstudio.si--color::before { color: #FF9E0F; }
.si-abbvie::before { content: "\ea12"; }
.si-abbvie.si--color::before { color: #071D49; }
.si-aboutdotme::before { content: "\ea13"; }
.si-aboutdotme.si--color::before { color: #333333; }
.si-abstract::before { content: "\ea14"; }
.si-abstract.si--color::before { color: #191A1B; }
.si-abusedotch::before { content: "\ea15"; }
.si-abusedotch.si--color::before { color: #00465B; }
.si-academia::before { content: "\ea16"; }
.si-academia.si--color::before { color: #41454A; }
.si-accenture::before { content: "\ea17"; }
.si-accenture.si--color::before { color: #A100FF; }
.si-accusoft::before { content: "\ea18"; }
.si-accusoft.si--color::before { color: #A9225C; }
.si-acer::before { content: "\ea19"; }
.si-acer.si--color::before { color: #83B81A; }
.si-acm::before { content: "\ea1a"; }
.si-acm.si--color::before { color: #0085CA; }
.si-actigraph::before { content: "\ea1b"; }
.si-actigraph.si--color::before { color: #0B2C4A; }
.si-activision::before { content: "\ea1c"; }
.si-activision.si--color::before { color: #000000; }
.si-activitypub::before { content: "\ea1d"; }
.si-activitypub.si--color::before { color: #F1007E; }
.si-actix::before { content: "\ea1e"; }
.si-actix.si--color::before { color: #000000; }
.si-acura::before { content: "\ea1f"; }
.si-acura.si--color::before { color: #000000; }
.si-adafruit::before { content: "\ea20"; }
.si-adafruit.si--color::before { color: #000000; }
.si-adblock::before { content: "\ea21"; }
.si-adblock.si--color::before { color: #F40D12; }
.si-adblockplus::before { content: "\ea22"; }
.si-adblockplus.si--color::before { color: #C70D2C; }
.si-adguard::before { content: "\ea23"; }
.si-adguard.si--color::before { color: #68BC71; }
.si-adidas::before { content: "\ea24"; }
.si-adidas.si--color::before { color: #000000; }
.si-adminer::before { content: "\ea25"; }
.si-adminer.si--color::before { color: #34567C; }
.si-adobe::before { content: "\ea26"; }
.si-adobe.si--color::before { color: #FF0000; }
.si-adobeacrobatreader::before { content: "\ea27"; }
.si-adobeacrobatreader.si--color::before { color: #EC1C24; }
.si-adobeaftereffects::before { content: "\ea28"; }
.si-adobeaftereffects.si--color::before { color: #9999FF; }
.si-adobeaudition::before { content: "\ea29"; }
.si-adobeaudition.si--color::before { color: #9999FF; }
.si-adobecreativecloud::before { content: "\ea2a"; }
.si-adobecreativecloud.si--color::before { color: #DA1F26; }
.si-adobedreamweaver::before { content: "\ea2b"; }
.si-adobedreamweaver.si--color::before { color: #FF61F6; }
.si-adobefonts::before { content: "\ea2c"; }
.si-adobefonts.si--color::before { color: #000B1D; }
.si-adobeillustrator::before { content: "\ea2d"; }
.si-adobeillustrator.si--color::before { color: #FF9A00; }
.si-adobeindesign::before { content: "\ea2e"; }
.si-adobeindesign.si--color::before { color: #FF3366; }
.si-adobelightroom::before { content: "\ea2f"; }
.si-adobelightroom.si--color::before { color: #31A8FF; }
.si-adobelightroomclassic::before { content: "\ea30"; }
.si-adobelightroomclassic.si--color::before { color: #31A8FF; }
.si-adobephotoshop::before { content: "\ea31"; }
.si-adobephotoshop.si--color::before { color: #31A8FF; }
.si-adobepremierepro::before { content: "\ea32"; }
.si-adobepremierepro.si--color::before { color: #9999FF; }
.si-adobexd::before { content: "\ea33"; }
.si-adobexd.si--color::before { color: #FF61F6; }
.si-adonisjs::before { content: "\ea34"; }
.si-adonisjs.si--color::before { color: #5A45FF; }
.si-adp::before { content: "\ea35"; }
.si-adp.si--color::before { color: #D0271D; }
.si-adroll::before { content: "\ea36"; }
.si-adroll.si--color::before { color: #0DBDFF; }
.si-adventofcode::before { content: "\ea37"; }
.si-adventofcode.si--color::before { color: #FFFF66; }
.si-adyen::before { content: "\ea38"; }
.si-adyen.si--color::before { color: #0ABF53; }
.si-aegisauthenticator::before { content: "\ea39"; }
.si-aegisauthenticator.si--color::before { color: #005E9D; }
.si-aerlingus::before { content: "\ea3a"; }
.si-aerlingus.si--color::before { color: #006272; }
.si-aeroflot::before { content: "\ea3b"; }
.si-aeroflot.si--color::before { color: #02458D; }
.si-aeromexico::before { content: "\ea3c"; }
.si-aeromexico.si--color::before { color: #0B2343; }
.si-aerospike::before { content: "\ea3d"; }
.si-aerospike.si--color::before { color: #C22127; }
.si-aew::before { content: "\ea3e"; }
.si-aew.si--color::before { color: #000000; }
.si-affine::before { content: "\ea3f"; }
.si-affine.si--color::before { color: #1E96EB; }
.si-affinity::before { content: "\ea40"; }
.si-affinity.si--color::before { color: #222324; }
.si-affinitydesigner::before { content: "\ea41"; }
.si-affinitydesigner.si--color::before { color: #134881; }
.si-affinityphoto::before { content: "\ea42"; }
.si-affinityphoto.si--color::before { color: #4E3188; }
.si-affinitypublisher::before { content: "\ea43"; }
.si-affinitypublisher.si--color::before { color: #891B26; }
.si-aframe::before { content: "\ea44"; }
.si-aframe.si--color::before { color: #EF2D5E; }
.si-afterpay::before { content: "\ea45"; }
.si-afterpay.si--color::before { color: #B2FCE4; }
.si-agora::before { content: "\ea46"; }
.si-agora.si--color::before { color: #099DFD; }
.si-aib::before { content: "\ea47"; }
.si-aib.si--color::before { color: #7F2B7B; }
.si-aidungeon::before { content: "\ea48"; }
.si-aidungeon.si--color::before { color: #000000; }
.si-aiohttp::before { content: "\ea49"; }
.si-aiohttp.si--color::before { color: #2C5BB4; }
.si-aiqfome::before { content: "\ea4a"; }
.si-aiqfome.si--color::before { color: #7A1FA2; }
.si-airasia::before { content: "\ea4b"; }
.si-airasia.si--color::before { color: #FF0000; }
.si-airbnb::before { content: "\ea4c"; }
.si-airbnb.si--color::before { color: #FF5A5F; }
.si-airbrake::before { content: "\ea4d"; }
.si-airbrake.si--color::before { color: #FFA500; }
.si-airbus::before { content: "\ea4e"; }
.si-airbus.si--color::before { color: #00205B; }
.si-airbyte::before { content: "\ea4f"; }
.si-airbyte.si--color::before { color: #615EFF; }
.si-aircall::before { content: "\ea50"; }
.si-aircall.si--color::before { color: #00B388; }
.si-aircanada::before { content: "\ea51"; }
.si-aircanada.si--color::before { color: #F01428; }
.si-airchina::before { content: "\ea52"; }
.si-airchina.si--color::before { color: #E30E17; }
.si-airfrance::before { content: "\ea53"; }
.si-airfrance.si--color::before { color: #002157; }
.si-airindia::before { content: "\ea54"; }
.si-airindia.si--color::before { color: #DA0E29; }
.si-airplayaudio::before { content: "\ea55"; }
.si-airplayaudio.si--color::before { color: #000000; }
.si-airplayvideo::before { content: "\ea56"; }
.si-airplayvideo.si--color::before { color: #000000; }
.si-airtable::before { content: "\ea57"; }
.si-airtable.si--color::before { color: #18BFFF; }
.si-airtel::before { content: "\ea58"; }
.si-airtel.si--color::before { color: #E40000; }
.si-airtransat::before { content: "\ea59"; }
.si-airtransat.si--color::before { color: #172B54; }
.si-ajv::before { content: "\ea5a"; }
.si-ajv.si--color::before { color: #23C8D2; }
.si-akamai::before { content: "\ea5b"; }
.si-akamai.si--color::before { color: #0096D6; }
.si-akaunting::before { content: "\ea5c"; }
.si-akaunting.si--color::before { color: #6DA252; }
.si-akiflow::before { content: "\ea5d"; }
.si-akiflow.si--color::before { color: #AF38F9; }
.si-alacritty::before { content: "\ea5e"; }
.si-alacritty.si--color::before { color: #F46D01; }
.si-alamy::before { content: "\ea5f"; }
.si-alamy.si--color::before { color: #00FF7B; }
.si-albertheijn::before { content: "\ea60"; }
.si-albertheijn.si--color::before { color: #04ACE6; }
.si-alby::before { content: "\ea61"; }
.si-alby.si--color::before { color: #FFDF6F; }
.si-alchemy::before { content: "\ea62"; }
.si-alchemy.si--color::before { color: #0C0C0E; }
.si-aldinord::before { content: "\ea63"; }
.si-aldinord.si--color::before { color: #2490D7; }
.si-aldisud::before { content: "\ea64"; }
.si-aldisud.si--color::before { color: #00005F; }
.si-alfaromeo::before { content: "\ea65"; }
.si-alfaromeo.si--color::before { color: #981E32; }
.si-alfred::before { content: "\ea66"; }
.si-alfred.si--color::before { color: #5C1F87; }
.si-algolia::before { content: "\ea67"; }
.si-algolia.si--color::before { color: #003DFF; }
.si-algorand::before { content: "\ea68"; }
.si-algorand.si--color::before { color: #000000; }
.si-alibabacloud::before { content: "\ea69"; }
.si-alibabacloud.si--color::before { color: #FF6A00; }
.si-alibabadotcom::before { content: "\ea6a"; }
.si-alibabadotcom.si--color::before { color: #FF6A00; }
.si-alienware::before { content: "\ea6b"; }
.si-alienware.si--color::before { color: #541BAE; }
.si-aliexpress::before { content: "\ea6c"; }
.si-aliexpress.si--color::before { color: #FF4747; }
.si-alipay::before { content: "\ea6d"; }
.si-alipay.si--color::before { color: #1677FF; }
.si-allegro::before { content: "\ea6e"; }
.si-allegro.si--color::before { color: #FF5A00; }
.si-alliedmodders::before { content: "\ea6f"; }
.si-alliedmodders.si--color::before { color: #1578D3; }
.si-allocine::before { content: "\ea70"; }
.si-allocine.si--color::before { color: #FECC00; }
.si-alltrails::before { content: "\ea71"; }
.si-alltrails.si--color::before { color: #142800; }
.si-almalinux::before { content: "\ea72"; }
.si-almalinux.si--color::before { color: #000000; }
.si-alpinedotjs::before { content: "\ea73"; }
.si-alpinedotjs.si--color::before { color: #8BC0D0; }
.si-alpinelinux::before { content: "\ea74"; }
.si-alpinelinux.si--color::before { color: #0D597F; }
.si-alteryx::before { content: "\ea75"; }
.si-alteryx.si--color::before { color: #0078C0; }
.si-altiumdesigner::before { content: "\ea76"; }
.si-altiumdesigner.si--color::before { color: #A5915F; }
.si-alwaysdata::before { content: "\ea77"; }
.si-alwaysdata.si--color::before { color: #E9568E; }
.si-alx::before { content: "\ea78"; }
.si-alx.si--color::before { color: #002B56; }
.si-amazon::before { content: "\ea79"; }
.si-amazon.si--color::before { color: #FF9900; }
.si-amazonalexa::before { content: "\ea7a"; }
.si-amazonalexa.si--color::before { color: #00CAFF; }
.si-amazonapigateway::before { content: "\ea7b"; }
.si-amazonapigateway.si--color::before { color: #FF4F8B; }
.si-amazoncloudwatch::before { content: "\ea7c"; }
.si-amazoncloudwatch.si--color::before { color: #FF4F8B; }
.si-amazoncognito::before { content: "\ea7d"; }
.si-amazoncognito.si--color::before { color: #DD344C; }
.si-amazondocumentdb::before { content: "\ea7e"; }
.si-amazondocumentdb.si--color::before { color: #C925D1; }
.si-amazondynamodb::before { content: "\ea7f"; }
.si-amazondynamodb.si--color::before { color: #4053D6; }
.si-amazonec2::before { content: "\ea80"; }
.si-amazonec2.si--color::before { color: #FF9900; }
.si-amazonecs::before { content: "\ea81"; }
.si-amazonecs.si--color::before { color: #FF9900; }
.si-amazoneks::before { content: "\ea82"; }
.si-amazoneks.si--color::before { color: #FF9900; }
.si-amazonelasticache::before { content: "\ea83"; }
.si-amazonelasticache.si--color::before { color: #C925D1; }
.si-amazonfiretv::before { content: "\ea84"; }
.si-amazonfiretv.si--color::before { color: #FC4C02; }
.si-amazongames::before { content: "\ea85"; }
.si-amazongames.si--color::before { color: #FF9900; }
.si-amazoniam::before { content: "\ea86"; }
.si-amazoniam.si--color::before { color: #DD344C; }
.si-amazonlumberyard::before { content: "\ea87"; }
.si-amazonlumberyard.si--color::before { color: #66459B; }
.si-amazonluna::before { content: "\ea88"; }
.si-amazonluna.si--color::before { color: #9146FF; }
.si-amazonmusic::before { content: "\ea89"; }
.si-amazonmusic.si--color::before { color: #46C3D0; }
.si-amazonpay::before { content: "\ea8a"; }
.si-amazonpay.si--color::before { color: #FF9900; }
.si-amazonprime::before { content: "\ea8b"; }
.si-amazonprime.si--color::before { color: #00A8E1; }
.si-amazonrds::before { content: "\ea8c"; }
.si-amazonrds.si--color::before { color: #527FFF; }
.si-amazonredshift::before { content: "\ea8d"; }
.si-amazonredshift.si--color::before { color: #8C4FFF; }
.si-amazonroute53::before { content: "\ea8e"; }
.si-amazonroute53.si--color::before { color: #8C4FFF; }
.si-amazons3::before { content: "\ea8f"; }
.si-amazons3.si--color::before { color: #569A31; }
.si-amazonsimpleemailservice::before { content: "\ea90"; }
.si-amazonsimpleemailservice.si--color::before { color: #DD344C; }
.si-amazonsqs::before { content: "\ea91"; }
.si-amazonsqs.si--color::before { color: #FF4F8B; }
.si-amazonwebservices::before { content: "\ea92"; }
.si-amazonwebservices.si--color::before { color: #232F3E; }
.si-amd::before { content: "\ea93"; }
.si-amd.si--color::before { color: #ED1C24; }
.si-ameba::before { content: "\ea94"; }
.si-ameba.si--color::before { color: #2D8C3C; }
.si-americanairlines::before { content: "\ea95"; }
.si-americanairlines.si--color::before { color: #0078D2; }
.si-americanexpress::before { content: "\ea96"; }
.si-americanexpress.si--color::before { color: #2E77BC; }
.si-amg::before { content: "\ea97"; }
.si-amg.si--color::before { color: #000000; }
.si-amp::before { content: "\ea98"; }
.si-amp.si--color::before { color: #005AF0; }
.si-amul::before { content: "\ea99"; }
.si-amul.si--color::before { color: #ED1D24; }
.si-ana::before { content: "\ea9a"; }
.si-ana.si--color::before { color: #13448F; }
.si-anaconda::before { content: "\ea9b"; }
.si-anaconda.si--color::before { color: #44A833; }
.si-analogue::before { content: "\ea9c"; }
.si-analogue.si--color::before { color: #1A1A1A; }
.si-andela::before { content: "\ea9d"; }
.si-andela.si--color::before { color: #173B3F; }
.si-android::before { content: "\ea9e"; }
.si-android.si--color::before { color: #34A853; }
.si-androidauto::before { content: "\ea9f"; }
.si-androidauto.si--color::before { color: #3DDC84; }
.si-androidstudio::before { content: "\eaa0"; }
.si-androidstudio.si--color::before { color: #3DDC84; }
.si-angular::before { content: "\eaa1"; }
.si-angular.si--color::before { color: #0F0F11; }
.si-anilist::before { content: "\eaa2"; }
.si-anilist.si--color::before { color: #02A9FF; }
.si-animalplanet::before { content: "\eaa3"; }
.si-animalplanet.si--color::before { color: #0073FF; }
.si-ankermake::before { content: "\eaa4"; }
.si-ankermake.si--color::before { color: #88F387; }
.si-anki::before { content: "\eaa5"; }
.si-anki.si--color::before { color: #80C2EE; }
.si-ansible::before { content: "\eaa6"; }
.si-ansible.si--color::before { color: #EE0000; }
.si-answer::before { content: "\eaa7"; }
.si-answer.si--color::before { color: #0033FF; }
.si-ansys::before { content: "\eaa8"; }
.si-ansys.si--color::before { color: #FFB71B; }
.si-anta::before { content: "\eaa9"; }
.si-anta.si--color::before { color: #D70010; }
.si-antdesign::before { content: "\eaaa"; }
.si-antdesign.si--color::before { color: #0170FE; }
.si-antena3::before { content: "\eaab"; }
.si-antena3.si--color::before { color: #FF7328; }
.si-anthropic::before { content: "\eaac"; }
.si-anthropic.si--color::before { color: #191919; }
.si-anycubic::before { content: "\eaad"; }
.si-anycubic.si--color::before { color: #476695; }
.si-anydesk::before { content: "\eaae"; }
.si-anydesk.si--color::before { color: #EF443B; }
.si-anytype::before { content: "\eaaf"; }
.si-anytype.si--color::before { color: #FF6A7B; }
.si-aol::before { content: "\eab0"; }
.si-aol.si--color::before { color: #3399FF; }
.si-apache::before { content: "\eab1"; }
.si-apache.si--color::before { color: #D22128; }
.si-apacheairflow::before { content: "\eab2"; }
.si-apacheairflow.si--color::before { color: #017CEE; }
.si-apacheant::before { content: "\eab3"; }
.si-apacheant.si--color::before { color: #A81C7D; }
.si-apachecassandra::before { content: "\eab4"; }
.si-apachecassandra.si--color::before { color: #1287B1; }
.si-apachecloudstack::before { content: "\eab5"; }
.si-apachecloudstack.si--color::before { color: #2AA5DC; }
.si-apachecordova::before { content: "\eab6"; }
.si-apachecordova.si--color::before { color: #E8E8E8; }
.si-apachecouchdb::before { content: "\eab7"; }
.si-apachecouchdb.si--color::before { color: #E42528; }
.si-apachedruid::before { content: "\eab8"; }
.si-apachedruid.si--color::before { color: #29F1FB; }
.si-apacheecharts::before { content: "\eab9"; }
.si-apacheecharts.si--color::before { color: #AA344D; }
.si-apacheflink::before { content: "\eaba"; }
.si-apacheflink.si--color::before { color: #E6526F; }
.si-apachefreemarker::before { content: "\eabb"; }
.si-apachefreemarker.si--color::before { color: #326CAC; }
.si-apachegroovy::before { content: "\eabc"; }
.si-apachegroovy.si--color::before { color: #4298B8; }
.si-apacheguacamole::before { content: "\eabd"; }
.si-apacheguacamole.si--color::before { color: #578B34; }
.si-apachehadoop::before { content: "\eabe"; }
.si-apachehadoop.si--color::before { color: #66CCFF; }
.si-apachehbase::before { content: "\eabf"; }
.si-apachehbase.si--color::before { color: #BE160C; }
.si-apachehive::before { content: "\eac0"; }
.si-apachehive.si--color::before { color: #FDEE21; }
.si-apachejmeter::before { content: "\eac1"; }
.si-apachejmeter.si--color::before { color: #D22128; }
.si-apachekafka::before { content: "\eac2"; }
.si-apachekafka.si--color::before { color: #231F20; }
.si-apachekylin::before { content: "\eac3"; }
.si-apachekylin.si--color::before { color: #F09D13; }
.si-apachemaven::before { content: "\eac4"; }
.si-apachemaven.si--color::before { color: #C71A36; }
.si-apachenetbeanside::before { content: "\eac5"; }
.si-apachenetbeanside.si--color::before { color: #1B6AC6; }
.si-apacheopenoffice::before { content: "\eac6"; }
.si-apacheopenoffice.si--color::before { color: #0E85CD; }
.si-apacheparquet::before { content: "\eac7"; }
.si-apacheparquet.si--color::before { color: #50ABF1; }
.si-apachepulsar::before { content: "\eac8"; }
.si-apachepulsar.si--color::before { color: #188FFF; }
.si-apacherocketmq::before { content: "\eac9"; }
.si-apacherocketmq.si--color::before { color: #D77310; }
.si-apachesolr::before { content: "\eaca"; }
.si-apachesolr.si--color::before { color: #D9411E; }
.si-apachespark::before { content: "\eacb"; }
.si-apachespark.si--color::before { color: #E25A1C; }
.si-apachestorm::before { content: "\eacc"; }
.si-apachestorm.si--color::before { color: #225593; }
.si-apachesuperset::before { content: "\eacd"; }
.si-apachesuperset.si--color::before { color: #20A6C9; }
.si-apachetomcat::before { content: "\eace"; }
.si-apachetomcat.si--color::before { color: #F8DC75; }
.si-aparat::before { content: "\eacf"; }
.si-aparat.si--color::before { color: #ED145B; }
.si-apifox::before { content: "\ead0"; }
.si-apifox.si--color::before { color: #F44A53; }
.si-apmterminals::before { content: "\ead1"; }
.si-apmterminals.si--color::before { color: #FF6441; }
.si-apollographql::before { content: "\ead2"; }
.si-apollographql.si--color::before { color: #311C87; }
.si-apostrophe::before { content: "\ead3"; }
.si-apostrophe.si--color::before { color: #6236FF; }
.si-appian::before { content: "\ead4"; }
.si-appian.si--color::before { color: #2322F0; }
.si-appium::before { content: "\ead5"; }
.si-appium.si--color::before { color: #EE376D; }
.si-apple::before { content: "\ead6"; }
.si-apple.si--color::before { color: #000000; }
.si-applearcade::before { content: "\ead7"; }
.si-applearcade.si--color::before { color: #000000; }
.si-applemusic::before { content: "\ead8"; }
.si-applemusic.si--color::before { color: #FA243C; }
.si-applenews::before { content: "\ead9"; }
.si-applenews.si--color::before { color: #FD415E; }
.si-applepay::before { content: "\eada"; }
.si-applepay.si--color::before { color: #000000; }
.si-applepodcasts::before { content: "\eadb"; }
.si-applepodcasts.si--color::before { color: #9933CC; }
.si-appletv::before { content: "\eadc"; }
.si-appletv.si--color::before { color: #000000; }
.si-appsignal::before { content: "\eadd"; }
.si-appsignal.si--color::before { color: #21375A; }
.si-appsmith::before { content: "\eade"; }
.si-appsmith.si--color::before { color: #2A2F3D; }
.si-appstore::before { content: "\eadf"; }
.si-appstore.si--color::before { color: #0D96F6; }
.si-appveyor::before { content: "\eae0"; }
.si-appveyor.si--color::before { color: #00B3E0; }
.si-appwrite::before { content: "\eae1"; }
.si-appwrite.si--color::before { color: #FD366E; }
.si-aqua::before { content: "\eae2"; }
.si-aqua.si--color::before { color: #1904DA; }
.si-aral::before { content: "\eae3"; }
.si-aral.si--color::before { color: #0063CB; }
.si-arangodb::before { content: "\eae4"; }
.si-arangodb.si--color::before { color: #DDE072; }
.si-arc::before { content: "\eae5"; }
.si-arc.si--color::before { color: #FCBFBD; }
.si-arcgis::before { content: "\eae6"; }
.si-arcgis.si--color::before { color: #2C7AC3; }
.si-archicad::before { content: "\eae7"; }
.si-archicad.si--color::before { color: #2D50A5; }
.si-archiveofourown::before { content: "\eae8"; }
.si-archiveofourown.si--color::before { color: #990000; }
.si-archlinux::before { content: "\eae9"; }
.si-archlinux.si--color::before { color: #1793D1; }
.si-ardour::before { content: "\eaea"; }
.si-ardour.si--color::before { color: #C61C3E; }
.si-arduino::before { content: "\eaeb"; }
.si-arduino.si--color::before { color: #00878F; }
.si-argo::before { content: "\eaec"; }
.si-argo.si--color::before { color: #EF7B4D; }
.si-argos::before { content: "\eaed"; }
.si-argos.si--color::before { color: #DA291C; }
.si-ariakit::before { content: "\eaee"; }
.si-ariakit.si--color::before { color: #007ACC; }
.si-arkecosystem::before { content: "\eaef"; }
.si-arkecosystem.si--color::before { color: #C9292C; }
.si-arlo::before { content: "\eaf0"; }
.si-arlo.si--color::before { color: #49B48A; }
.si-arm::before { content: "\eaf1"; }
.si-arm.si--color::before { color: #0091BD; }
.si-armkeil::before { content: "\eaf2"; }
.si-armkeil.si--color::before { color: #394049; }
.si-arstechnica::before { content: "\eaf3"; }
.si-arstechnica.si--color::before { color: #FF4E00; }
.si-artifacthub::before { content: "\eaf4"; }
.si-artifacthub.si--color::before { color: #417598; }
.si-artixlinux::before { content: "\eaf5"; }
.si-artixlinux.si--color::before { color: #10A0CC; }
.si-artstation::before { content: "\eaf6"; }
.si-artstation.si--color::before { color: #13AFF0; }
.si-arxiv::before { content: "\eaf7"; }
.si-arxiv.si--color::before { color: #B31B1B; }
.si-asahilinux::before { content: "\eaf8"; }
.si-asahilinux.si--color::before { color: #A61200; }
.si-asana::before { content: "\eaf9"; }
.si-asana.si--color::before { color: #F06A6A; }
.si-asciidoctor::before { content: "\eafa"; }
.si-asciidoctor.si--color::before { color: #E40046; }
.si-asciinema::before { content: "\eafb"; }
.si-asciinema.si--color::before { color: #D40000; }
.si-asda::before { content: "\eafc"; }
.si-asda.si--color::before { color: #68A51C; }
.si-aseprite::before { content: "\eafd"; }
.si-aseprite.si--color::before { color: #7D929E; }
.si-askfm::before { content: "\eafe"; }
.si-askfm.si--color::before { color: #DB3552; }
.si-assemblyscript::before { content: "\eaff"; }
.si-assemblyscript.si--color::before { color: #007AAC; }
.si-asterisk::before { content: "\eb00"; }
.si-asterisk.si--color::before { color: #F68F1E; }
.si-astonmartin::before { content: "\eb01"; }
.si-astonmartin.si--color::before { color: #00665E; }
.si-astra::before { content: "\eb02"; }
.si-astra.si--color::before { color: #5C2EDE; }
.si-astral::before { content: "\eb03"; }
.si-astral.si--color::before { color: #261230; }
.si-astro::before { content: "\eb04"; }
.si-astro.si--color::before { color: #BC52EE; }
.si-asus::before { content: "\eb05"; }
.si-asus.si--color::before { color: #000000; }
.si-atandt::before { content: "\eb06"; }
.si-atandt.si--color::before { color: #009FDB; }
.si-atari::before { content: "\eb07"; }
.si-atari.si--color::before { color: #E4202E; }
.si-atlassian::before { content: "\eb08"; }
.si-atlassian.si--color::before { color: #0052CC; }
.si-auchan::before { content: "\eb09"; }
.si-auchan.si--color::before { color: #D6180B; }
.si-audacity::before { content: "\eb0a"; }
.si-audacity.si--color::before { color: #0000CC; }
.si-audi::before { content: "\eb0b"; }
.si-audi.si--color::before { color: #BB0A30; }
.si-audible::before { content: "\eb0c"; }
.si-audible.si--color::before { color: #F8991C; }
.si-audiobookshelf::before { content: "\eb0d"; }
.si-audiobookshelf.si--color::before { color: #82612C; }
.si-audioboom::before { content: "\eb0e"; }
.si-audioboom.si--color::before { color: #007CE2; }
.si-audiomack::before { content: "\eb0f"; }
.si-audiomack.si--color::before { color: #FFA200; }
.si-audiotechnica::before { content: "\eb10"; }
.si-audiotechnica.si--color::before { color: #000000; }
.si-aurelia::before { content: "\eb11"; }
.si-aurelia.si--color::before { color: #ED2B88; }
.si-auth0::before { content: "\eb12"; }
.si-auth0.si--color::before { color: #EB5424; }
.si-authelia::before { content: "\eb13"; }
.si-authelia.si--color::before { color: #113155; }
.si-authentik::before { content: "\eb14"; }
.si-authentik.si--color::before { color: #FD4B2D; }
.si-authy::before { content: "\eb15"; }
.si-authy.si--color::before { color: #EC1C24; }
.si-autocad::before { content: "\eb16"; }
.si-autocad.si--color::before { color: #E51050; }
.si-autocannon::before { content: "\eb17"; }
.si-autocannon.si--color::before { color: #3BA4B7; }
.si-autodesk::before { content: "\eb18"; }
.si-autodesk.si--color::before { color: #000000; }
.si-autodeskmaya::before { content: "\eb19"; }
.si-autodeskmaya.si--color::before { color: #37A5CC; }
.si-autodeskrevit::before { content: "\eb1a"; }
.si-autodeskrevit.si--color::before { color: #186BFF; }
.si-autohotkey::before { content: "\eb1b"; }
.si-autohotkey.si--color::before { color: #334455; }
.si-autoit::before { content: "\eb1c"; }
.si-autoit.si--color::before { color: #5D83AC; }
.si-automattic::before { content: "\eb1d"; }
.si-automattic.si--color::before { color: #3499CD; }
.si-autoprefixer::before { content: "\eb1e"; }
.si-autoprefixer.si--color::before { color: #DD3735; }
.si-autozone::before { content: "\eb1f"; }
.si-autozone.si--color::before { color: #D52B1E; }
.si-avajs::before { content: "\eb20"; }
.si-avajs.si--color::before { color: #4B4B77; }
.si-avast::before { content: "\eb21"; }
.si-avast.si--color::before { color: #FF7800; }
.si-avianca::before { content: "\eb22"; }
.si-avianca.si--color::before { color: #FF0000; }
.si-avira::before { content: "\eb23"; }
.si-avira.si--color::before { color: #E02027; }
.si-awesomelists::before { content: "\eb24"; }
.si-awesomelists.si--color::before { color: #FC60A8; }
.si-awesomewm::before { content: "\eb25"; }
.si-awesomewm.si--color::before { color: #535D6C; }
.si-awsamplify::before { content: "\eb26"; }
.si-awsamplify.si--color::before { color: #FF9900; }
.si-awselasticloadbalancing::before { content: "\eb27"; }
.si-awselasticloadbalancing.si--color::before { color: #8C4FFF; }
.si-awsfargate::before { content: "\eb28"; }
.si-awsfargate.si--color::before { color: #FF9900; }
.si-awslambda::before { content: "\eb29"; }
.si-awslambda.si--color::before { color: #FF9900; }
.si-awsorganizations::before { content: "\eb2a"; }
.si-awsorganizations.si--color::before { color: #E7157B; }
.si-awssecretsmanager::before { content: "\eb2b"; }
.si-awssecretsmanager.si--color::before { color: #DD344C; }
.si-awwwards::before { content: "\eb2c"; }
.si-awwwards.si--color::before { color: #222222; }
.si-axios::before { content: "\eb2d"; }
.si-axios.si--color::before { color: #5A29E4; }
.si-babel::before { content: "\eb2e"; }
.si-babel.si--color::before { color: #F9DC3E; }
.si-babelio::before { content: "\eb2f"; }
.si-babelio.si--color::before { color: #FBB91E; }
.si-babylondotjs::before { content: "\eb30"; }
.si-babylondotjs.si--color::before { color: #BB464B; }
.si-backblaze::before { content: "\eb31"; }
.si-backblaze.si--color::before { color: #E21E29; }
.si-backbonedotjs::before { content: "\eb32"; }
.si-backbonedotjs.si--color::before { color: #0071B5; }
.si-backendless::before { content: "\eb33"; }
.si-backendless.si--color::before { color: #1D77BD; }
.si-backstage::before { content: "\eb34"; }
.si-backstage.si--color::before { color: #9BF0E1; }
.si-badoo::before { content: "\eb35"; }
.si-badoo.si--color::before { color: #783BF9; }
.si-baidu::before { content: "\eb36"; }
.si-baidu.si--color::before { color: #2932E1; }
.si-bakalari::before { content: "\eb37"; }
.si-bakalari.si--color::before { color: #00A2E2; }
.si-bamboo::before { content: "\eb38"; }
.si-bamboo.si--color::before { color: #0052CC; }
.si-bambulab::before { content: "\eb39"; }
.si-bambulab.si--color::before { color: #00AE42; }
.si-bandcamp::before { content: "\eb3a"; }
.si-bandcamp.si--color::before { color: #408294; }
.si-bandlab::before { content: "\eb3b"; }
.si-bandlab.si--color::before { color: #F12C18; }
.si-bandrautomation::before { content: "\eb3c"; }
.si-bandrautomation.si--color::before { color: #FF8800; }
.si-bandsintown::before { content: "\eb3d"; }
.si-bandsintown.si--color::before { color: #00CEC8; }
.si-bankofamerica::before { content: "\eb3e"; }
.si-bankofamerica.si--color::before { color: #012169; }
.si-barclays::before { content: "\eb3f"; }
.si-barclays.si--color::before { color: #00AEEF; }
.si-baremetrics::before { content: "\eb40"; }
.si-baremetrics.si--color::before { color: #6078FF; }
.si-barmenia::before { content: "\eb41"; }
.si-barmenia.si--color::before { color: #009FE3; }
.si-basecamp::before { content: "\eb42"; }
.si-basecamp.si--color::before { color: #1D2D35; }
.si-basicattentiontoken::before { content: "\eb43"; }
.si-basicattentiontoken.si--color::before { color: #80247B; }
.si-bastyon::before { content: "\eb44"; }
.si-bastyon.si--color::before { color: #00A4FF; }
.si-bat::before { content: "\eb45"; }
.si-bat.si--color::before { color: #31369E; }
.si-bata::before { content: "\eb46"; }
.si-bata.si--color::before { color: #DD282E; }
.si-battledotnet::before { content: "\eb47"; }
.si-battledotnet.si--color::before { color: #4381C3; }
.si-bazel::before { content: "\eb48"; }
.si-bazel.si--color::before { color: #43A047; }
.si-beatport::before { content: "\eb49"; }
.si-beatport.si--color::before { color: #01FF95; }
.si-beats::before { content: "\eb4a"; }
.si-beats.si--color::before { color: #005571; }
.si-beatsbydre::before { content: "\eb4b"; }
.si-beatsbydre.si--color::before { color: #E01F3D; }
.si-beatstars::before { content: "\eb4c"; }
.si-beatstars.si--color::before { color: #EB0000; }
.si-beekeeperstudio::before { content: "\eb4d"; }
.si-beekeeperstudio.si--color::before { color: #FAD83B; }
.si-behance::before { content: "\eb4e"; }
.si-behance.si--color::before { color: #1769FF; }
.si-beijingsubway::before { content: "\eb4f"; }
.si-beijingsubway.si--color::before { color: #004A9D; }
.si-bem::before { content: "\eb50"; }
.si-bem.si--color::before { color: #000000; }
.si-bentley::before { content: "\eb51"; }
.si-bentley.si--color::before { color: #333333; }
.si-bento::before { content: "\eb52"; }
.si-bento.si--color::before { color: #768CFF; }
.si-bentobox::before { content: "\eb53"; }
.si-bentobox.si--color::before { color: #F15541; }
.si-bentoml::before { content: "\eb54"; }
.si-bentoml.si--color::before { color: #000000; }
.si-bereal::before { content: "\eb55"; }
.si-bereal.si--color::before { color: #000000; }
.si-betfair::before { content: "\eb56"; }
.si-betfair.si--color::before { color: #FFB80B; }
.si-betterstack::before { content: "\eb57"; }
.si-betterstack.si--color::before { color: #000000; }
.si-bevy::before { content: "\eb58"; }
.si-bevy.si--color::before { color: #232326; }
.si-bigbasket::before { content: "\eb59"; }
.si-bigbasket.si--color::before { color: #A5CD39; }
.si-bigbluebutton::before { content: "\eb5a"; }
.si-bigbluebutton.si--color::before { color: #283274; }
.si-bigcartel::before { content: "\eb5b"; }
.si-bigcartel.si--color::before { color: #222222; }
.si-bigcommerce::before { content: "\eb5c"; }
.si-bigcommerce.si--color::before { color: #121118; }
.si-bilibili::before { content: "\eb5d"; }
.si-bilibili.si--color::before { color: #00A1D6; }
.si-billboard::before { content: "\eb5e"; }
.si-billboard.si--color::before { color: #000000; }
.si-bim::before { content: "\eb5f"; }
.si-bim.si--color::before { color: #EB1928; }
.si-binance::before { content: "\eb60"; }
.si-binance.si--color::before { color: #F0B90B; }
.si-biolink::before { content: "\eb61"; }
.si-biolink.si--color::before { color: #000000; }
.si-biome::before { content: "\eb62"; }
.si-biome.si--color::before { color: #60A5FA; }
.si-bisecthosting::before { content: "\eb63"; }
.si-bisecthosting.si--color::before { color: #0D1129; }
.si-bit::before { content: "\eb64"; }
.si-bit.si--color::before { color: #592EC1; }
.si-bitbucket::before { content: "\eb65"; }
.si-bitbucket.si--color::before { color: #0052CC; }
.si-bitcoin::before { content: "\eb66"; }
.si-bitcoin.si--color::before { color: #F7931A; }
.si-bitcoincash::before { content: "\eb67"; }
.si-bitcoincash.si--color::before { color: #0AC18E; }
.si-bitcoinsv::before { content: "\eb68"; }
.si-bitcoinsv.si--color::before { color: #EAB300; }
.si-bitcomet::before { content: "\eb69"; }
.si-bitcomet.si--color::before { color: #F49923; }
.si-bitdefender::before { content: "\eb6a"; }
.si-bitdefender.si--color::before { color: #ED1C24; }
.si-bitly::before { content: "\eb6b"; }
.si-bitly.si--color::before { color: #EE6123; }
.si-bitrise::before { content: "\eb6c"; }
.si-bitrise.si--color::before { color: #683D87; }
.si-bittorrent::before { content: "\eb6d"; }
.si-bittorrent.si--color::before { color: #050505; }
.si-bitwarden::before { content: "\eb6e"; }
.si-bitwarden.si--color::before { color: #175DDC; }
.si-bitwig::before { content: "\eb6f"; }
.si-bitwig.si--color::before { color: #FF5A00; }
.si-blackberry::before { content: "\eb70"; }
.si-blackberry.si--color::before { color: #000000; }
.si-blackmagicdesign::before { content: "\eb71"; }
.si-blackmagicdesign.si--color::before { color: #FFA200; }
.si-blazemeter::before { content: "\eb72"; }
.si-blazemeter.si--color::before { color: #CA2133; }
.si-blazor::before { content: "\eb73"; }
.si-blazor.si--color::before { color: #512BD4; }
.si-blender::before { content: "\eb74"; }
.si-blender.si--color::before { color: #E87D0D; }
.si-blockbench::before { content: "\eb75"; }
.si-blockbench.si--color::before { color: #1E93D9; }
.si-blockchaindotcom::before { content: "\eb76"; }
.si-blockchaindotcom.si--color::before { color: #121D33; }
.si-blogger::before { content: "\eb77"; }
.si-blogger.si--color::before { color: #FF5722; }
.si-bloglovin::before { content: "\eb78"; }
.si-bloglovin.si--color::before { color: #000000; }
.si-blueprint::before { content: "\eb79"; }
.si-blueprint.si--color::before { color: #137CBD; }
.si-bluesky::before { content: "\eb7a"; }
.si-bluesky.si--color::before { color: #0285FF; }
.si-bluesound::before { content: "\eb7b"; }
.si-bluesound.si--color::before { color: #0F131E; }
.si-bluetooth::before { content: "\eb7c"; }
.si-bluetooth.si--color::before { color: #0082FC; }
.si-bmcsoftware::before { content: "\eb7d"; }
.si-bmcsoftware.si--color::before { color: #FE5000; }
.si-bmw::before { content: "\eb7e"; }
.si-bmw.si--color::before { color: #0066B1; }
.si-bnbchain::before { content: "\eb7f"; }
.si-bnbchain.si--color::before { color: #F0B90B; }
.si-boardgamegeek::before { content: "\eb80"; }
.si-boardgamegeek.si--color::before { color: #FF5100; }
.si-boat::before { content: "\eb81"; }
.si-boat.si--color::before { color: #E20722; }
.si-boehringeringelheim::before { content: "\eb82"; }
.si-boehringeringelheim.si--color::before { color: #00E47C; }
.si-boeing::before { content: "\eb83"; }
.si-boeing.si--color::before { color: #1D439C; }
.si-bombardier::before { content: "\eb84"; }
.si-bombardier.si--color::before { color: #020203; }
.si-bookalope::before { content: "\eb85"; }
.si-bookalope.si--color::before { color: #DC2829; }
.si-bookbub::before { content: "\eb86"; }
.si-bookbub.si--color::before { color: #F44336; }
.si-bookmeter::before { content: "\eb87"; }
.si-bookmeter.si--color::before { color: #64BC4B; }
.si-bookmyshow::before { content: "\eb88"; }
.si-bookmyshow.si--color::before { color: #C4242B; }
.si-bookstack::before { content: "\eb89"; }
.si-bookstack.si--color::before { color: #0288D1; }
.si-boost::before { content: "\eb8a"; }
.si-boost.si--color::before { color: #F7901E; }
.si-boosty::before { content: "\eb8b"; }
.si-boosty.si--color::before { color: #F15F2C; }
.si-boots::before { content: "\eb8c"; }
.si-boots.si--color::before { color: #05054B; }
.si-bootstrap::before { content: "\eb8d"; }
.si-bootstrap.si--color::before { color: #7952B3; }
.si-borgbackup::before { content: "\eb8e"; }
.si-borgbackup.si--color::before { color: #00DD00; }
.si-bosch::before { content: "\eb8f"; }
.si-bosch.si--color::before { color: #EA0016; }
.si-bose::before { content: "\eb90"; }
.si-bose.si--color::before { color: #000000; }
.si-botblecms::before { content: "\eb91"; }
.si-botblecms.si--color::before { color: #205081; }
.si-boulanger::before { content: "\eb92"; }
.si-boulanger.si--color::before { color: #FD5300; }
.si-bower::before { content: "\eb93"; }
.si-bower.si--color::before { color: #EF5734; }
.si-box::before { content: "\eb94"; }
.si-box.si--color::before { color: #0061D5; }
.si-boxysvg::before { content: "\eb95"; }
.si-boxysvg.si--color::before { color: #3584E3; }
.si-braintree::before { content: "\eb96"; }
.si-braintree.si--color::before { color: #000000; }
.si-brandfolder::before { content: "\eb97"; }
.si-brandfolder.si--color::before { color: #40D1F5; }
.si-brave::before { content: "\eb98"; }
.si-brave.si--color::before { color: #FB542B; }
.si-breaker::before { content: "\eb99"; }
.si-breaker.si--color::before { color: #003DAD; }
.si-brenntag::before { content: "\eb9a"; }
.si-brenntag.si--color::before { color: #1A0033; }
.si-brevo::before { content: "\eb9b"; }
.si-brevo.si--color::before { color: #0B996E; }
.si-brex::before { content: "\eb9c"; }
.si-brex.si--color::before { color: #212121; }
.si-bricks::before { content: "\eb9d"; }
.si-bricks.si--color::before { color: #FFD54D; }
.si-britishairways::before { content: "\eb9e"; }
.si-britishairways.si--color::before { color: #2E5C99; }
.si-broadcom::before { content: "\eb9f"; }
.si-broadcom.si--color::before { color: #E31837; }
.si-bruno::before { content: "\eba0"; }
.si-bruno.si--color::before { color: #F4AA41; }
.si-bsd::before { content: "\eba1"; }
.si-bsd.si--color::before { color: #AB2B28; }
.si-bspwm::before { content: "\eba2"; }
.si-bspwm.si--color::before { color: #2E2E2E; }
.si-bt::before { content: "\eba3"; }
.si-bt.si--color::before { color: #6400AA; }
.si-buddy::before { content: "\eba4"; }
.si-buddy.si--color::before { color: #1A86FD; }
.si-budibase::before { content: "\eba5"; }
.si-budibase.si--color::before { color: #000000; }
.si-buefy::before { content: "\eba6"; }
.si-buefy.si--color::before { color: #7957D5; }
.si-buffer::before { content: "\eba7"; }
.si-buffer.si--color::before { color: #231F20; }
.si-bugatti::before { content: "\eba8"; }
.si-bugatti.si--color::before { color: #000000; }
.si-bugcrowd::before { content: "\eba9"; }
.si-bugcrowd.si--color::before { color: #F26822; }
.si-bugsnag::before { content: "\ebaa"; }
.si-bugsnag.si--color::before { color: #4949E4; }
.si-buhl::before { content: "\ebab"; }
.si-buhl.si--color::before { color: #023E84; }
.si-buildkite::before { content: "\ebac"; }
.si-buildkite.si--color::before { color: #14CC80; }
.si-bukalapak::before { content: "\ebad"; }
.si-bukalapak.si--color::before { color: #E31E52; }
.si-bulma::before { content: "\ebae"; }
.si-bulma.si--color::before { color: #00D1B2; }
.si-bun::before { content: "\ebaf"; }
.si-bun.si--color::before { color: #000000; }
.si-bungie::before { content: "\ebb0"; }
.si-bungie.si--color::before { color: #0075BB; }
.si-bunq::before { content: "\ebb1"; }
.si-bunq.si--color::before { color: #3394D7; }
.si-burgerking::before { content: "\ebb2"; }
.si-burgerking.si--color::before { color: #D62300; }
.si-burpsuite::before { content: "\ebb3"; }
.si-burpsuite.si--color::before { color: #FF6633; }
.si-burton::before { content: "\ebb4"; }
.si-burton.si--color::before { color: #000000; }
.si-buymeacoffee::before { content: "\ebb5"; }
.si-buymeacoffee.si--color::before { color: #FFDD00; }
.si-buysellads::before { content: "\ebb6"; }
.si-buysellads.si--color::before { color: #EB4714; }
.si-buzzfeed::before { content: "\ebb7"; }
.si-buzzfeed.si--color::before { color: #EE3322; }
.si-bvg::before { content: "\ebb8"; }
.si-bvg.si--color::before { color: #F0D722; }
.si-byjus::before { content: "\ebb9"; }
.si-byjus.si--color::before { color: #813588; }
.si-bytedance::before { content: "\ebba"; }
.si-bytedance.si--color::before { color: #3C8CFF; }
.si-c::before { content: "\ebbb"; }
.si-c.si--color::before { color: #A8B9CC; }
.si-cachet::before { content: "\ebbc"; }
.si-cachet.si--color::before { color: #7ED321; }
.si-caddy::before { content: "\ebbd"; }
.si-caddy.si--color::before { color: #1F88C0; }
.si-cadillac::before { content: "\ebbe"; }
.si-cadillac.si--color::before { color: #000000; }
.si-cafepress::before { content: "\ebbf"; }
.si-cafepress.si--color::before { color: #58A616; }
.si-caffeine::before { content: "\ebc0"; }
.si-caffeine.si--color::before { color: #0000FF; }
.si-cairographics::before { content: "\ebc1"; }
.si-cairographics.si--color::before { color: #F39914; }
.si-cairometro::before { content: "\ebc2"; }
.si-cairometro.si--color::before { color: #C10C0C; }
.si-cakephp::before { content: "\ebc3"; }
.si-cakephp.si--color::before { color: #D33C43; }
.si-caldotcom::before { content: "\ebc4"; }
.si-caldotcom.si--color::before { color: #292929; }
.si-calendly::before { content: "\ebc5"; }
.si-calendly.si--color::before { color: #006BFF; }
.si-calibreweb::before { content: "\ebc6"; }
.si-calibreweb.si--color::before { color: #45B29D; }
.si-campaignmonitor::before { content: "\ebc7"; }
.si-campaignmonitor.si--color::before { color: #111324; }
.si-camunda::before { content: "\ebc8"; }
.si-camunda.si--color::before { color: #FC5D0D; }
.si-canonical::before { content: "\ebc9"; }
.si-canonical.si--color::before { color: #E95420; }
.si-canva::before { content: "\ebca"; }
.si-canva.si--color::before { color: #00C4CC; }
.si-canvas::before { content: "\ebcb"; }
.si-canvas.si--color::before { color: #E72429; }
.si-capacitor::before { content: "\ebcc"; }
.si-capacitor.si--color::before { color: #119EFF; }
.si-cardano::before { content: "\ebcd"; }
.si-cardano.si--color::before { color: #0133AD; }
.si-carlsberggroup::before { content: "\ebce"; }
.si-carlsberggroup.si--color::before { color: #00321E; }
.si-carrd::before { content: "\ebcf"; }
.si-carrd.si--color::before { color: #596CAF; }
.si-carrefour::before { content: "\ebd0"; }
.si-carrefour.si--color::before { color: #004E9F; }
.si-carthrottle::before { content: "\ebd1"; }
.si-carthrottle.si--color::before { color: #FF9C42; }
.si-carto::before { content: "\ebd2"; }
.si-carto.si--color::before { color: #EB1510; }
.si-cashapp::before { content: "\ebd3"; }
.si-cashapp.si--color::before { color: #00C244; }
.si-castbox::before { content: "\ebd4"; }
.si-castbox.si--color::before { color: #F55B23; }
.si-castorama::before { content: "\ebd5"; }
.si-castorama.si--color::before { color: #0078D7; }
.si-castro::before { content: "\ebd6"; }
.si-castro.si--color::before { color: #00B265; }
.si-caterpillar::before { content: "\ebd7"; }
.si-caterpillar.si--color::before { color: #FFCD11; }
.si-cbc::before { content: "\ebd8"; }
.si-cbc.si--color::before { color: #E60505; }
.si-cbs::before { content: "\ebd9"; }
.si-cbs.si--color::before { color: #033963; }
.si-ccleaner::before { content: "\ebda"; }
.si-ccleaner.si--color::before { color: #CB2D29; }
.si-cdprojekt::before { content: "\ebdb"; }
.si-cdprojekt.si--color::before { color: #DC0D15; }
.si-celery::before { content: "\ebdc"; }
.si-celery.si--color::before { color: #37814A; }
.si-celestron::before { content: "\ebdd"; }
.si-celestron.si--color::before { color: #F47216; }
.si-centos::before { content: "\ebde"; }
.si-centos.si--color::before { color: #262577; }
.si-ceph::before { content: "\ebdf"; }
.si-ceph.si--color::before { color: #EF5C55; }
.si-cesium::before { content: "\ebe0"; }
.si-cesium.si--color::before { color: #6CADDF; }
.si-chai::before { content: "\ebe1"; }
.si-chai.si--color::before { color: #A30701; }
.si-chainguard::before { content: "\ebe2"; }
.si-chainguard.si--color::before { color: #4445E7; }
.si-chainlink::before { content: "\ebe3"; }
.si-chainlink.si--color::before { color: #375BD2; }
.si-chakraui::before { content: "\ebe4"; }
.si-chakraui.si--color::before { color: #319795; }
.si-channel4::before { content: "\ebe5"; }
.si-channel4.si--color::before { color: #AAFF89; }
.si-charles::before { content: "\ebe6"; }
.si-charles.si--color::before { color: #F3F5F5; }
.si-chartdotjs::before { content: "\ebe7"; }
.si-chartdotjs.si--color::before { color: #FF6384; }
.si-chartmogul::before { content: "\ebe8"; }
.si-chartmogul.si--color::before { color: #13324B; }
.si-chase::before { content: "\ebe9"; }
.si-chase.si--color::before { color: #117ACA; }
.si-chatbot::before { content: "\ebea"; }
.si-chatbot.si--color::before { color: #0066FF; }
.si-chatwoot::before { content: "\ebeb"; }
.si-chatwoot.si--color::before { color: #1F93FF; }
.si-checkio::before { content: "\ebec"; }
.si-checkio.si--color::before { color: #008DB6; }
.si-checkmarx::before { content: "\ebed"; }
.si-checkmarx.si--color::before { color: #54B848; }
.si-checkmk::before { content: "\ebee"; }
.si-checkmk.si--color::before { color: #15D1A0; }
.si-chedraui::before { content: "\ebef"; }
.si-chedraui.si--color::before { color: #E0832F; }
.si-cheerio::before { content: "\ebf0"; }
.si-cheerio.si--color::before { color: #E88C1F; }
.si-chef::before { content: "\ebf1"; }
.si-chef.si--color::before { color: #F09820; }
.si-chemex::before { content: "\ebf2"; }
.si-chemex.si--color::before { color: #4D2B1A; }
.si-chessdotcom::before { content: "\ebf3"; }
.si-chessdotcom.si--color::before { color: #81B64C; }
.si-chevrolet::before { content: "\ebf4"; }
.si-chevrolet.si--color::before { color: #CD9834; }
.si-chianetwork::before { content: "\ebf5"; }
.si-chianetwork.si--color::before { color: #5ECE71; }
.si-chinaeasternairlines::before { content: "\ebf6"; }
.si-chinaeasternairlines.si--color::before { color: #1A2477; }
.si-chinasouthernairlines::before { content: "\ebf7"; }
.si-chinasouthernairlines.si--color::before { color: #008BCB; }
.si-chocolatey::before { content: "\ebf8"; }
.si-chocolatey.si--color::before { color: #80B5E3; }
.si-chromatic::before { content: "\ebf9"; }
.si-chromatic.si--color::before { color: #FC521F; }
.si-chromecast::before { content: "\ebfa"; }
.si-chromecast.si--color::before { color: #999999; }
.si-chromewebstore::before { content: "\ebfb"; }
.si-chromewebstore.si--color::before { color: #4285F4; }
.si-chrysler::before { content: "\ebfc"; }
.si-chrysler.si--color::before { color: #000000; }
.si-chupachups::before { content: "\ebfd"; }
.si-chupachups.si--color::before { color: #CF103E; }
.si-cilium::before { content: "\ebfe"; }
.si-cilium.si--color::before { color: #F8C517; }
.si-cinema4d::before { content: "\ebff"; }
.si-cinema4d.si--color::before { color: #011A6A; }
.si-circle::before { content: "\ec00"; }
.si-circle.si--color::before { color: #8669AE; }
.si-circleci::before { content: "\ec01"; }
.si-circleci.si--color::before { color: #343434; }
.si-circuitverse::before { content: "\ec02"; }
.si-circuitverse.si--color::before { color: #42B883; }
.si-cirrusci::before { content: "\ec03"; }
.si-cirrusci.si--color::before { color: #4051B5; }
.si-cisco::before { content: "\ec04"; }
.si-cisco.si--color::before { color: #1BA0D7; }
.si-citrix::before { content: "\ec05"; }
.si-citrix.si--color::before { color: #452170; }
.si-citroen::before { content: "\ec06"; }
.si-citroen.si--color::before { color: #DA291C; }
.si-civicrm::before { content: "\ec07"; }
.si-civicrm.si--color::before { color: #81C459; }
.si-civo::before { content: "\ec08"; }
.si-civo.si--color::before { color: #239DFF; }
.si-ckeditor4::before { content: "\ec09"; }
.si-ckeditor4.si--color::before { color: #0287D0; }
.si-clarifai::before { content: "\ec0a"; }
.si-clarifai.si--color::before { color: #1955FF; }
.si-claris::before { content: "\ec0b"; }
.si-claris.si--color::before { color: #000000; }
.si-clarivate::before { content: "\ec0c"; }
.si-clarivate.si--color::before { color: #93FF9E; }
.si-clerk::before { content: "\ec0d"; }
.si-clerk.si--color::before { color: #6C47FF; }
.si-clevercloud::before { content: "\ec0e"; }
.si-clevercloud.si--color::before { color: #171C36; }
.si-clickhouse::before { content: "\ec0f"; }
.si-clickhouse.si--color::before { color: #FFCC01; }
.si-clickup::before { content: "\ec10"; }
.si-clickup.si--color::before { color: #7B68EE; }
.si-clion::before { content: "\ec11"; }
.si-clion.si--color::before { color: #000000; }
.si-cliqz::before { content: "\ec12"; }
.si-cliqz.si--color::before { color: #00AEF0; }
.si-clockify::before { content: "\ec13"; }
.si-clockify.si--color::before { color: #03A9F4; }
.si-clojure::before { content: "\ec14"; }
.si-clojure.si--color::before { color: #5881D8; }
.si-cloud66::before { content: "\ec15"; }
.si-cloud66.si--color::before { color: #3C72B9; }
.si-cloudbees::before { content: "\ec16"; }
.si-cloudbees.si--color::before { color: #1997B5; }
.si-cloudcannon::before { content: "\ec17"; }
.si-cloudcannon.si--color::before { color: #407AFC; }
.si-cloudera::before { content: "\ec18"; }
.si-cloudera.si--color::before { color: #F96702; }
.si-cloudflare::before { content: "\ec19"; }
.si-cloudflare.si--color::before { color: #F38020; }
.si-cloudflarepages::before { content: "\ec1a"; }
.si-cloudflarepages.si--color::before { color: #F38020; }
.si-cloudflareworkers::before { content: "\ec1b"; }
.si-cloudflareworkers.si--color::before { color: #F38020; }
.si-cloudfoundry::before { content: "\ec1c"; }
.si-cloudfoundry.si--color::before { color: #0C9ED5; }
.si-cloudinary::before { content: "\ec1d"; }
.si-cloudinary.si--color::before { color: #3448C5; }
.si-cloudron::before { content: "\ec1e"; }
.si-cloudron.si--color::before { color: #03A9F4; }
.si-cloudsmith::before { content: "\ec1f"; }
.si-cloudsmith.si--color::before { color: #2A6FE1; }
.si-cloudways::before { content: "\ec20"; }
.si-cloudways.si--color::before { color: #2C39BD; }
.si-clubforce::before { content: "\ec21"; }
.si-clubforce.si--color::before { color: #191176; }
.si-clubhouse::before { content: "\ec22"; }
.si-clubhouse.si--color::before { color: #FFE450; }
.si-clyp::before { content: "\ec23"; }
.si-clyp.si--color::before { color: #3CBDB1; }
.si-cmake::before { content: "\ec24"; }
.si-cmake.si--color::before { color: #064F8C; }
.si-cncf::before { content: "\ec25"; }
.si-cncf.si--color::before { color: #231F20; }
.si-cnet::before { content: "\ec26"; }
.si-cnet.si--color::before { color: #E71D1D; }
.si-cnn::before { content: "\ec27"; }
.si-cnn.si--color::before { color: #CC0000; }
.si-cocacola::before { content: "\ec28"; }
.si-cocacola.si--color::before { color: #D00013; }
.si-cockpit::before { content: "\ec29"; }
.si-cockpit.si--color::before { color: #0066CC; }
.si-cockroachlabs::before { content: "\ec2a"; }
.si-cockroachlabs.si--color::before { color: #6933FF; }
.si-cocoapods::before { content: "\ec2b"; }
.si-cocoapods.si--color::before { color: #EE3322; }
.si-cocos::before { content: "\ec2c"; }
.si-cocos.si--color::before { color: #55C2E1; }
.si-coda::before { content: "\ec2d"; }
.si-coda.si--color::before { color: #F46A54; }
.si-codacy::before { content: "\ec2e"; }
.si-codacy.si--color::before { color: #222F29; }
.si-codeberg::before { content: "\ec2f"; }
.si-codeberg.si--color::before { color: #2185D0; }
.si-codeblocks::before { content: "\ec30"; }
.si-codeblocks.si--color::before { color: #41AD48; }
.si-codecademy::before { content: "\ec31"; }
.si-codecademy.si--color::before { color: #1F4056; }
.si-codeceptjs::before { content: "\ec32"; }
.si-codeceptjs.si--color::before { color: #F6E05E; }
.si-codechef::before { content: "\ec33"; }
.si-codechef.si--color::before { color: #5B4638; }
.si-codeclimate::before { content: "\ec34"; }
.si-codeclimate.si--color::before { color: #000000; }
.si-codecov::before { content: "\ec35"; }
.si-codecov.si--color::before { color: #F01F7A; }
.si-codefactor::before { content: "\ec36"; }
.si-codefactor.si--color::before { color: #F44A6A; }
.si-codeforces::before { content: "\ec37"; }
.si-codeforces.si--color::before { color: #1F8ACB; }
.si-codefresh::before { content: "\ec38"; }
.si-codefresh.si--color::before { color: #08B1AB; }
.si-codeigniter::before { content: "\ec39"; }
.si-codeigniter.si--color::before { color: #EF4223; }
.si-codeium::before { content: "\ec3a"; }
.si-codeium.si--color::before { color: #09B6A2; }
.si-codemagic::before { content: "\ec3b"; }
.si-codemagic.si--color::before { color: #F45E3F; }
.si-codementor::before { content: "\ec3c"; }
.si-codementor.si--color::before { color: #003648; }
.si-codemirror::before { content: "\ec3d"; }
.si-codemirror.si--color::before { color: #D30707; }
.si-codenewbie::before { content: "\ec3e"; }
.si-codenewbie.si--color::before { color: #9013FE; }
.si-codepen::before { content: "\ec3f"; }
.si-codepen.si--color::before { color: #000000; }
.si-codeproject::before { content: "\ec40"; }
.si-codeproject.si--color::before { color: #FF9900; }
.si-coder::before { content: "\ec41"; }
.si-coder.si--color::before { color: #000000; }
.si-codersrank::before { content: "\ec42"; }
.si-codersrank.si--color::before { color: #67A4AC; }
.si-coderwall::before { content: "\ec43"; }
.si-coderwall.si--color::before { color: #3E8DCC; }
.si-codesandbox::before { content: "\ec44"; }
.si-codesandbox.si--color::before { color: #151515; }
.si-codeship::before { content: "\ec45"; }
.si-codeship.si--color::before { color: #004466; }
.si-codesignal::before { content: "\ec46"; }
.si-codesignal.si--color::before { color: #1062FB; }
.si-codestream::before { content: "\ec47"; }
.si-codestream.si--color::before { color: #008C99; }
.si-codewars::before { content: "\ec48"; }
.si-codewars.si--color::before { color: #B1361E; }
.si-codingame::before { content: "\ec49"; }
.si-codingame.si--color::before { color: #F2BB13; }
.si-codingninjas::before { content: "\ec4a"; }
.si-codingninjas.si--color::before { color: #DD6620; }
.si-codio::before { content: "\ec4b"; }
.si-codio.si--color::before { color: #4574E0; }
.si-coffeescript::before { content: "\ec4c"; }
.si-coffeescript.si--color::before { color: #2F2625; }
.si-coggle::before { content: "\ec4d"; }
.si-coggle.si--color::before { color: #9ED56B; }
.si-cognizant::before { content: "\ec4e"; }
.si-cognizant.si--color::before { color: #1A4CA1; }
.si-coil::before { content: "\ec4f"; }
.si-coil.si--color::before { color: #000000; }
.si-coinbase::before { content: "\ec50"; }
.si-coinbase.si--color::before { color: #0052FF; }
.si-coinmarketcap::before { content: "\ec51"; }
.si-coinmarketcap.si--color::before { color: #17181B; }
.si-comicfury::before { content: "\ec52"; }
.si-comicfury.si--color::before { color: #79BD42; }
.si-comma::before { content: "\ec53"; }
.si-comma.si--color::before { color: #51FF00; }
.si-commerzbank::before { content: "\ec54"; }
.si-commerzbank.si--color::before { color: #FFCC33; }
.si-commitlint::before { content: "\ec55"; }
.si-commitlint.si--color::before { color: #000000; }
.si-commodore::before { content: "\ec56"; }
.si-commodore.si--color::before { color: #1E2A4E; }
.si-commonworkflowlanguage::before { content: "\ec57"; }
.si-commonworkflowlanguage.si--color::before { color: #B5314C; }
.si-compilerexplorer::before { content: "\ec58"; }
.si-compilerexplorer.si--color::before { color: #67C52A; }
.si-composer::before { content: "\ec59"; }
.si-composer.si--color::before { color: #885630; }
.si-comptia::before { content: "\ec5a"; }
.si-comptia.si--color::before { color: #C8202F; }
.si-comsol::before { content: "\ec5b"; }
.si-comsol.si--color::before { color: #368CCB; }
.si-conan::before { content: "\ec5c"; }
.si-conan.si--color::before { color: #6699CB; }
.si-concourse::before { content: "\ec5d"; }
.si-concourse.si--color::before { color: #3398DC; }
.si-condaforge::before { content: "\ec5e"; }
.si-condaforge.si--color::before { color: #000000; }
.si-conekta::before { content: "\ec5f"; }
.si-conekta.si--color::before { color: #0A1837; }
.si-confluence::before { content: "\ec60"; }
.si-confluence.si--color::before { color: #172B4D; }
.si-construct3::before { content: "\ec61"; }
.si-construct3.si--color::before { color: #00FFDA; }
.si-consul::before { content: "\ec62"; }
.si-consul.si--color::before { color: #F24C53; }
.si-contactlesspayment::before { content: "\ec63"; }
.si-contactlesspayment.si--color::before { color: #000000; }
.si-containerd::before { content: "\ec64"; }
.si-containerd.si--color::before { color: #575757; }
.si-contao::before { content: "\ec65"; }
.si-contao.si--color::before { color: #F47C00; }
.si-contentful::before { content: "\ec66"; }
.si-contentful.si--color::before { color: #2478CC; }
.si-contentstack::before { content: "\ec67"; }
.si-contentstack.si--color::before { color: #E74C3D; }
.si-contributorcovenant::before { content: "\ec68"; }
.si-contributorcovenant.si--color::before { color: #5E0D73; }
.si-conventionalcommits::before { content: "\ec69"; }
.si-conventionalcommits.si--color::before { color: #FE5196; }
.si-convertio::before { content: "\ec6a"; }
.si-convertio.si--color::before { color: #FF3333; }
.si-cookiecutter::before { content: "\ec6b"; }
.si-cookiecutter.si--color::before { color: #D4AA00; }
.si-coolermaster::before { content: "\ec6c"; }
.si-coolermaster.si--color::before { color: #1E1E28; }
.si-coop::before { content: "\ec6d"; }
.si-coop.si--color::before { color: #00B1E7; }
.si-copaairlines::before { content: "\ec6e"; }
.si-copaairlines.si--color::before { color: #0032A0; }
.si-coppel::before { content: "\ec6f"; }
.si-coppel.si--color::before { color: #0266AE; }
.si-cora::before { content: "\ec70"; }
.si-cora.si--color::before { color: #E61845; }
.si-coreldraw::before { content: "\ec71"; }
.si-coreldraw.si--color::before { color: #000000; }
.si-coronaengine::before { content: "\ec72"; }
.si-coronaengine.si--color::before { color: #F96F29; }
.si-coronarenderer::before { content: "\ec73"; }
.si-coronarenderer.si--color::before { color: #E6502A; }
.si-corsair::before { content: "\ec74"; }
.si-corsair.si--color::before { color: #000000; }
.si-couchbase::before { content: "\ec75"; }
.si-couchbase.si--color::before { color: #EA2328; }
.si-counterstrike::before { content: "\ec76"; }
.si-counterstrike.si--color::before { color: #000000; }
.si-countingworkspro::before { content: "\ec77"; }
.si-countingworkspro.si--color::before { color: #2E3084; }
.si-coursera::before { content: "\ec78"; }
.si-coursera.si--color::before { color: #0056D2; }
.si-coveralls::before { content: "\ec79"; }
.si-coveralls.si--color::before { color: #3F5767; }
.si-cpanel::before { content: "\ec7a"; }
.si-cpanel.si--color::before { color: #FF6C2C; }
.si-cplusplus::before { content: "\ec7b"; }
.si-cplusplus.si--color::before { color: #00599C; }
.si-cplusplusbuilder::before { content: "\ec7c"; }
.si-cplusplusbuilder.si--color::before { color: #E62431; }
.si-craftcms::before { content: "\ec7d"; }
.si-craftcms.si--color::before { color: #E5422B; }
.si-craftsman::before { content: "\ec7e"; }
.si-craftsman.si--color::before { color: #D6001C; }
.si-cratedb::before { content: "\ec7f"; }
.si-cratedb.si--color::before { color: #009DC7; }
.si-crayon::before { content: "\ec80"; }
.si-crayon.si--color::before { color: #FF6A4C; }
.si-creality::before { content: "\ec81"; }
.si-creality.si--color::before { color: #000000; }
.si-createreactapp::before { content: "\ec82"; }
.si-createreactapp.si--color::before { color: #09D3AC; }
.si-creativecommons::before { content: "\ec83"; }
.si-creativecommons.si--color::before { color: #EF9421; }
.si-creativetechnology::before { content: "\ec84"; }
.si-creativetechnology.si--color::before { color: #000000; }
.si-credly::before { content: "\ec85"; }
.si-credly.si--color::before { color: #FF6B00; }
.si-crehana::before { content: "\ec86"; }
.si-crehana.si--color::before { color: #4B22F4; }
.si-crewunited::before { content: "\ec87"; }
.si-crewunited.si--color::before { color: #000000; }
.si-criticalrole::before { content: "\ec88"; }
.si-criticalrole.si--color::before { color: #000000; }
.si-crowdin::before { content: "\ec89"; }
.si-crowdin.si--color::before { color: #2E3340; }
.si-crowdsource::before { content: "\ec8a"; }
.si-crowdsource.si--color::before { color: #4285F4; }
.si-crunchbase::before { content: "\ec8b"; }
.si-crunchbase.si--color::before { color: #0288D1; }
.si-crunchyroll::before { content: "\ec8c"; }
.si-crunchyroll.si--color::before { color: #F47521; }
.si-cryengine::before { content: "\ec8d"; }
.si-cryengine.si--color::before { color: #000000; }
.si-cryptpad::before { content: "\ec8e"; }
.si-cryptpad.si--color::before { color: #0087FF; }
.si-crystal::before { content: "\ec8f"; }
.si-crystal.si--color::before { color: #000000; }
.si-css3::before { content: "\ec90"; }
.si-css3.si--color::before { color: #1572B6; }
.si-cssdesignawards::before { content: "\ec91"; }
.si-cssdesignawards.si--color::before { color: #280FEE; }
.si-cssmodules::before { content: "\ec92"; }
.si-cssmodules.si--color::before { color: #000000; }
.si-csswizardry::before { content: "\ec93"; }
.si-csswizardry.si--color::before { color: #F43059; }
.si-cts::before { content: "\ec94"; }
.si-cts.si--color::before { color: #E53236; }
.si-cucumber::before { content: "\ec95"; }
.si-cucumber.si--color::before { color: #23D96C; }
.si-cultura::before { content: "\ec96"; }
.si-cultura.si--color::before { color: #1D2C54; }
.si-curl::before { content: "\ec97"; }
.si-curl.si--color::before { color: #073551; }
.si-curseforge::before { content: "\ec98"; }
.si-curseforge.si--color::before { color: #F16436; }
.si-customink::before { content: "\ec99"; }
.si-customink.si--color::before { color: #4051B5; }
.si-cyberdefenders::before { content: "\ec9a"; }
.si-cyberdefenders.si--color::before { color: #335EEA; }
.si-cycling74::before { content: "\ec9b"; }
.si-cycling74.si--color::before { color: #111111; }
.si-cypress::before { content: "\ec9c"; }
.si-cypress.si--color::before { color: #69D3A7; }
.si-cytoscapedotjs::before { content: "\ec9d"; }
.si-cytoscapedotjs.si--color::before { color: #F7DF1E; }
.si-d::before { content: "\ec9e"; }
.si-d.si--color::before { color: #B03931; }
.si-d3dotjs::before { content: "\ec9f"; }
.si-d3dotjs.si--color::before { color: #F9A03C; }
.si-dacia::before { content: "\eca0"; }
.si-dacia.si--color::before { color: #646B52; }
.si-daf::before { content: "\eca1"; }
.si-daf.si--color::before { color: #00529B; }
.si-dailydotdev::before { content: "\eca2"; }
.si-dailydotdev.si--color::before { color: #CE3DF3; }
.si-dailymotion::before { content: "\eca3"; }
.si-dailymotion.si--color::before { color: #0A0A0A; }
.si-daisyui::before { content: "\eca4"; }
.si-daisyui.si--color::before { color: #5A0EF8; }
.si-dapr::before { content: "\eca5"; }
.si-dapr.si--color::before { color: #0D2192; }
.si-darkreader::before { content: "\eca6"; }
.si-darkreader.si--color::before { color: #141E24; }
.si-dart::before { content: "\eca7"; }
.si-dart.si--color::before { color: #0175C2; }
.si-darty::before { content: "\eca8"; }
.si-darty.si--color::before { color: #EB1B23; }
.si-daserste::before { content: "\eca9"; }
.si-daserste.si--color::before { color: #001A4B; }
.si-dash::before { content: "\ecaa"; }
.si-dash.si--color::before { color: #008DE4; }
.si-dashlane::before { content: "\ecab"; }
.si-dashlane.si--color::before { color: #0E353D; }
.si-dask::before { content: "\ecac"; }
.si-dask.si--color::before { color: #FC6E6B; }
.si-dassaultsystemes::before { content: "\ecad"; }
.si-dassaultsystemes.si--color::before { color: #005386; }
.si-databricks::before { content: "\ecae"; }
.si-databricks.si--color::before { color: #FF3621; }
.si-datacamp::before { content: "\ecaf"; }
.si-datacamp.si--color::before { color: #03EF62; }
.si-datadog::before { content: "\ecb0"; }
.si-datadog.si--color::before { color: #632CA6; }
.si-datadotai::before { content: "\ecb1"; }
.si-datadotai.si--color::before { color: #000000; }
.si-datagrip::before { content: "\ecb2"; }
.si-datagrip.si--color::before { color: #000000; }
.si-dataiku::before { content: "\ecb3"; }
.si-dataiku.si--color::before { color: #2AB1AC; }
.si-datastax::before { content: "\ecb4"; }
.si-datastax.si--color::before { color: #000000; }
.si-datefns::before { content: "\ecb5"; }
.si-datefns.si--color::before { color: #770C56; }
.si-datev::before { content: "\ecb6"; }
.si-datev.si--color::before { color: #9BD547; }
.si-datocms::before { content: "\ecb7"; }
.si-datocms.si--color::before { color: #FF7751; }
.si-datto::before { content: "\ecb8"; }
.si-datto.si--color::before { color: #199ED9; }
.si-davinciresolve::before { content: "\ecb9"; }
.si-davinciresolve.si--color::before { color: #233A51; }
.si-dazhongdianping::before { content: "\ecba"; }
.si-dazhongdianping.si--color::before { color: #FF6633; }
.si-dazn::before { content: "\ecbb"; }
.si-dazn.si--color::before { color: #F8F8F5; }
.si-dbeaver::before { content: "\ecbc"; }
.si-dbeaver.si--color::before { color: #382923; }
.si-dblp::before { content: "\ecbd"; }
.si-dblp.si--color::before { color: #004F9F; }
.si-dbt::before { content: "\ecbe"; }
.si-dbt.si--color::before { color: #FF694B; }
.si-dcentertainment::before { content: "\ecbf"; }
.si-dcentertainment.si--color::before { color: #0078F0; }
.si-debian::before { content: "\ecc0"; }
.si-debian.si--color::before { color: #A81D33; }
.si-decapcms::before { content: "\ecc1"; }
.si-decapcms.si--color::before { color: #FF0082; }
.si-decentraland::before { content: "\ecc2"; }
.si-decentraland.si--color::before { color: #FF2D55; }
.si-dedge::before { content: "\ecc3"; }
.si-dedge.si--color::before { color: #432975; }
.si-deepcool::before { content: "\ecc4"; }
.si-deepcool.si--color::before { color: #068584; }
.si-deepgram::before { content: "\ecc5"; }
.si-deepgram.si--color::before { color: #13EF93; }
.si-deepin::before { content: "\ecc6"; }
.si-deepin.si--color::before { color: #007CFF; }
.si-deepl::before { content: "\ecc7"; }
.si-deepl.si--color::before { color: #0F2B46; }
.si-deepnote::before { content: "\ecc8"; }
.si-deepnote.si--color::before { color: #3793EF; }
.si-delicious::before { content: "\ecc9"; }
.si-delicious.si--color::before { color: #0000FF; }
.si-deliveroo::before { content: "\ecca"; }
.si-deliveroo.si--color::before { color: #00CCBC; }
.si-dell::before { content: "\eccb"; }
.si-dell.si--color::before { color: #007DB8; }
.si-delonghi::before { content: "\eccc"; }
.si-delonghi.si--color::before { color: #072240; }
.si-delphi::before { content: "\eccd"; }
.si-delphi.si--color::before { color: #E62431; }
.si-delta::before { content: "\ecce"; }
.si-delta.si--color::before { color: #003366; }
.si-deluge::before { content: "\eccf"; }
.si-deluge.si--color::before { color: #094491; }
.si-deno::before { content: "\ecd0"; }
.si-deno.si--color::before { color: #000000; }
.si-denon::before { content: "\ecd1"; }
.si-denon.si--color::before { color: #0B131A; }
.si-dependabot::before { content: "\ecd2"; }
.si-dependabot.si--color::before { color: #025E8C; }
.si-dependencycheck::before { content: "\ecd3"; }
.si-dependencycheck.si--color::before { color: #F78D0A; }
.si-depositphotos::before { content: "\ecd4"; }
.si-depositphotos.si--color::before { color: #000000; }
.si-derspiegel::before { content: "\ecd5"; }
.si-derspiegel.si--color::before { color: #E64415; }
.si-deutschebahn::before { content: "\ecd6"; }
.si-deutschebahn.si--color::before { color: #F01414; }
.si-deutschebank::before { content: "\ecd7"; }
.si-deutschebank.si--color::before { color: #0018A8; }
.si-deutschepost::before { content: "\ecd8"; }
.si-deutschepost.si--color::before { color: #FFCC00; }
.si-devdotto::before { content: "\ecd9"; }
.si-devdotto.si--color::before { color: #0A0A0A; }
.si-devexpress::before { content: "\ecda"; }
.si-devexpress.si--color::before { color: #FF7200; }
.si-deviantart::before { content: "\ecdb"; }
.si-deviantart.si--color::before { color: #05CC47; }
.si-devpost::before { content: "\ecdc"; }
.si-devpost.si--color::before { color: #003E54; }
.si-devrant::before { content: "\ecdd"; }
.si-devrant.si--color::before { color: #F99A66; }
.si-dgraph::before { content: "\ecde"; }
.si-dgraph.si--color::before { color: #E50695; }
.si-dhl::before { content: "\ecdf"; }
.si-dhl.si--color::before { color: #FFCC00; }
.si-diagramsdotnet::before { content: "\ece0"; }
.si-diagramsdotnet.si--color::before { color: #F08705; }
.si-dialogflow::before { content: "\ece1"; }
.si-dialogflow.si--color::before { color: #FF9800; }
.si-diaspora::before { content: "\ece2"; }
.si-diaspora.si--color::before { color: #000000; }
.si-dictionarydotcom::before { content: "\ece3"; }
.si-dictionarydotcom.si--color::before { color: #0049D7; }
.si-digg::before { content: "\ece4"; }
.si-digg.si--color::before { color: #000000; }
.si-digikeyelectronics::before { content: "\ece5"; }
.si-digikeyelectronics.si--color::before { color: #CC0000; }
.si-digitalocean::before { content: "\ece6"; }
.si-digitalocean.si--color::before { color: #0080FF; }
.si-dinersclub::before { content: "\ece7"; }
.si-dinersclub.si--color::before { color: #004C97; }
.si-dior::before { content: "\ece8"; }
.si-dior.si--color::before { color: #000000; }
.si-directus::before { content: "\ece9"; }
.si-directus.si--color::before { color: #263238; }
.si-discogs::before { content: "\ecea"; }
.si-discogs.si--color::before { color: #333333; }
.si-discord::before { content: "\eceb"; }
.si-discord.si--color::before { color: #5865F2; }
.si-discourse::before { content: "\ecec"; }
.si-discourse.si--color::before { color: #000000; }
.si-discover::before { content: "\eced"; }
.si-discover.si--color::before { color: #FF6000; }
.si-disqus::before { content: "\ecee"; }
.si-disqus.si--color::before { color: #2E9FFF; }
.si-disroot::before { content: "\ecef"; }
.si-disroot.si--color::before { color: #50162D; }
.si-distrokid::before { content: "\ecf0"; }
.si-distrokid.si--color::before { color: #231F20; }
.si-django::before { content: "\ecf1"; }
.si-django.si--color::before { color: #092E20; }
.si-dji::before { content: "\ecf2"; }
.si-dji.si--color::before { color: #000000; }
.si-dlib::before { content: "\ecf3"; }
.si-dlib.si--color::before { color: #008000; }
.si-dlna::before { content: "\ecf4"; }
.si-dlna.si--color::before { color: #48A842; }
.si-dm::before { content: "\ecf5"; }
.si-dm.si--color::before { color: #002878; }
.si-docker::before { content: "\ecf6"; }
.si-docker.si--color::before { color: #2496ED; }
.si-docsdotrs::before { content: "\ecf7"; }
.si-docsdotrs.si--color::before { color: #000000; }
.si-docsify::before { content: "\ecf8"; }
.si-docsify.si--color::before { color: #2ECE53; }
.si-doctrine::before { content: "\ecf9"; }
.si-doctrine.si--color::before { color: #FC6A31; }
.si-docusaurus::before { content: "\ecfa"; }
.si-docusaurus.si--color::before { color: #3ECC5F; }
.si-dogecoin::before { content: "\ecfb"; }
.si-dogecoin.si--color::before { color: #C2A633; }
.si-doi::before { content: "\ecfc"; }
.si-doi.si--color::before { color: #FAB70C; }
.si-dolby::before { content: "\ecfd"; }
.si-dolby.si--color::before { color: #000000; }
.si-doordash::before { content: "\ecfe"; }
.si-doordash.si--color::before { color: #FF3008; }
.si-dota2::before { content: "\ecff"; }
.si-dota2.si--color::before { color: #BF2E1A; }
.si-dotenv::before { content: "\ed00"; }
.si-dotenv.si--color::before { color: #ECD53F; }
.si-dotnet::before { content: "\ed01"; }
.si-dotnet.si--color::before { color: #512BD4; }
.si-douban::before { content: "\ed02"; }
.si-douban.si--color::before { color: #2D963D; }
.si-doubanread::before { content: "\ed03"; }
.si-doubanread.si--color::before { color: #389EAC; }
.si-dovecot::before { content: "\ed04"; }
.si-dovecot.si--color::before { color: #54BCAB; }
.si-dovetail::before { content: "\ed05"; }
.si-dovetail.si--color::before { color: #190041; }
.si-doxygen::before { content: "\ed06"; }
.si-doxygen.si--color::before { color: #2C4AA8; }
.si-dpd::before { content: "\ed07"; }
.si-dpd.si--color::before { color: #DC0032; }
.si-dragonframe::before { content: "\ed08"; }
.si-dragonframe.si--color::before { color: #D4911E; }
.si-draugiemdotlv::before { content: "\ed09"; }
.si-draugiemdotlv.si--color::before { color: #FF6600; }
.si-dreamstime::before { content: "\ed0a"; }
.si-dreamstime.si--color::before { color: #50A901; }
.si-dribbble::before { content: "\ed0b"; }
.si-dribbble.si--color::before { color: #EA4C89; }
.si-drizzle::before { content: "\ed0c"; }
.si-drizzle.si--color::before { color: #C5F74F; }
.si-drone::before { content: "\ed0d"; }
.si-drone.si--color::before { color: #212121; }
.si-drooble::before { content: "\ed0e"; }
.si-drooble.si--color::before { color: #19C4BE; }
.si-dropbox::before { content: "\ed0f"; }
.si-dropbox.si--color::before { color: #0061FF; }
.si-drupal::before { content: "\ed10"; }
.si-drupal.si--color::before { color: #0678BE; }
.si-dsautomobiles::before { content: "\ed11"; }
.si-dsautomobiles.si--color::before { color: #1D1717; }
.si-dts::before { content: "\ed12"; }
.si-dts.si--color::before { color: #F98B2B; }
.si-dtube::before { content: "\ed13"; }
.si-dtube.si--color::before { color: #F01A30; }
.si-ducati::before { content: "\ed14"; }
.si-ducati.si--color::before { color: #CC0000; }
.si-duckdb::before { content: "\ed15"; }
.si-duckdb.si--color::before { color: #FFF000; }
.si-duckduckgo::before { content: "\ed16"; }
.si-duckduckgo.si--color::before { color: #DE5833; }
.si-dungeonsanddragons::before { content: "\ed17"; }
.si-dungeonsanddragons.si--color::before { color: #ED1C24; }
.si-dunked::before { content: "\ed18"; }
.si-dunked.si--color::before { color: #2DA9D7; }
.si-dunzo::before { content: "\ed19"; }
.si-dunzo.si--color::before { color: #00D290; }
.si-duolingo::before { content: "\ed1a"; }
.si-duolingo.si--color::before { color: #58CC02; }
.si-duplicati::before { content: "\ed1b"; }
.si-duplicati.si--color::before { color: #1E3A8A; }
.si-dvc::before { content: "\ed1c"; }
.si-dvc.si--color::before { color: #13ADC7; }
.si-dwavesystems::before { content: "\ed1d"; }
.si-dwavesystems.si--color::before { color: #008CD7; }
.si-dwm::before { content: "\ed1e"; }
.si-dwm.si--color::before { color: #1177AA; }
.si-dynatrace::before { content: "\ed1f"; }
.si-dynatrace.si--color::before { color: #1496FF; }
.si-e::before { content: "\ed20"; }
.si-e.si--color::before { color: #000000; }
.si-e3::before { content: "\ed21"; }
.si-e3.si--color::before { color: #E73D2F; }
.si-ea::before { content: "\ed22"; }
.si-ea.si--color::before { color: #000000; }
.si-eagle::before { content: "\ed23"; }
.si-eagle.si--color::before { color: #0072EF; }
.si-easyeda::before { content: "\ed24"; }
.si-easyeda.si--color::before { color: #1765F6; }
.si-easyjet::before { content: "\ed25"; }
.si-easyjet.si--color::before { color: #FF6600; }
.si-ebay::before { content: "\ed26"; }
.si-ebay.si--color::before { color: #E53238; }
.si-ebox::before { content: "\ed27"; }
.si-ebox.si--color::before { color: #BE2323; }
.si-eclipseadoptium::before { content: "\ed28"; }
.si-eclipseadoptium.si--color::before { color: #FF1464; }
.si-eclipseche::before { content: "\ed29"; }
.si-eclipseche.si--color::before { color: #525C86; }
.si-eclipseide::before { content: "\ed2a"; }
.si-eclipseide.si--color::before { color: #2C2255; }
.si-eclipsejetty::before { content: "\ed2b"; }
.si-eclipsejetty.si--color::before { color: #FC390E; }
.si-eclipsemosquitto::before { content: "\ed2c"; }
.si-eclipsemosquitto.si--color::before { color: #3C5280; }
.si-eclipsevertdotx::before { content: "\ed2d"; }
.si-eclipsevertdotx.si--color::before { color: #782A90; }
.si-ecovacs::before { content: "\ed2e"; }
.si-ecovacs.si--color::before { color: #1E384B; }
.si-edeka::before { content: "\ed2f"; }
.si-edeka.si--color::before { color: #1B66B3; }
.si-edgeimpulse::before { content: "\ed30"; }
.si-edgeimpulse.si--color::before { color: #3B47C2; }
.si-editorconfig::before { content: "\ed31"; }
.si-editorconfig.si--color::before { color: #FEFEFE; }
.si-edotleclerc::before { content: "\ed32"; }
.si-edotleclerc.si--color::before { color: #0066CC; }
.si-educative::before { content: "\ed33"; }
.si-educative.si--color::before { color: #4951F5; }
.si-edx::before { content: "\ed34"; }
.si-edx.si--color::before { color: #02262B; }
.si-egghead::before { content: "\ed35"; }
.si-egghead.si--color::before { color: #FCFBFA; }
.si-egnyte::before { content: "\ed36"; }
.si-egnyte.si--color::before { color: #00968F; }
.si-eight::before { content: "\ed37"; }
.si-eight.si--color::before { color: #0054FF; }
.si-eightsleep::before { content: "\ed38"; }
.si-eightsleep.si--color::before { color: #262729; }
.si-ejs::before { content: "\ed39"; }
.si-ejs.si--color::before { color: #B4CA65; }
.si-elastic::before { content: "\ed3a"; }
.si-elastic.si--color::before { color: #005571; }
.si-elasticcloud::before { content: "\ed3b"; }
.si-elasticcloud.si--color::before { color: #005571; }
.si-elasticsearch::before { content: "\ed3c"; }
.si-elasticsearch.si--color::before { color: #005571; }
.si-elasticstack::before { content: "\ed3d"; }
.si-elasticstack.si--color::before { color: #005571; }
.si-elavon::before { content: "\ed3e"; }
.si-elavon.si--color::before { color: #0C2074; }
.si-electron::before { content: "\ed3f"; }
.si-electron.si--color::before { color: #47848F; }
.si-electronbuilder::before { content: "\ed40"; }
.si-electronbuilder.si--color::before { color: #000000; }
.si-electronfiddle::before { content: "\ed41"; }
.si-electronfiddle.si--color::before { color: #E79537; }
.si-elegoo::before { content: "\ed42"; }
.si-elegoo.si--color::before { color: #2C3A83; }
.si-element::before { content: "\ed43"; }
.si-element.si--color::before { color: #0DBD8B; }
.si-elementary::before { content: "\ed44"; }
.si-elementary.si--color::before { color: #64BAFF; }
.si-elementor::before { content: "\ed45"; }
.si-elementor.si--color::before { color: #92003B; }
.si-eleventy::before { content: "\ed46"; }
.si-eleventy.si--color::before { color: #222222; }
.si-elgato::before { content: "\ed47"; }
.si-elgato.si--color::before { color: #101010; }
.si-elixir::before { content: "\ed48"; }
.si-elixir.si--color::before { color: #4B275F; }
.si-eljueves::before { content: "\ed49"; }
.si-eljueves.si--color::before { color: #BE312E; }
.si-ello::before { content: "\ed4a"; }
.si-ello.si--color::before { color: #000000; }
.si-elm::before { content: "\ed4b"; }
.si-elm.si--color::before { color: #1293D8; }
.si-elsevier::before { content: "\ed4c"; }
.si-elsevier.si--color::before { color: #FF6C00; }
.si-embarcadero::before { content: "\ed4d"; }
.si-embarcadero.si--color::before { color: #ED1F35; }
.si-embark::before { content: "\ed4e"; }
.si-embark.si--color::before { color: #000000; }
.si-emberdotjs::before { content: "\ed4f"; }
.si-emberdotjs.si--color::before { color: #E04E39; }
.si-emby::before { content: "\ed50"; }
.si-emby.si--color::before { color: #52B54B; }
.si-emirates::before { content: "\ed51"; }
.si-emirates.si--color::before { color: #D71921; }
.si-emlakjet::before { content: "\ed52"; }
.si-emlakjet.si--color::before { color: #0AE524; }
.si-empirekred::before { content: "\ed53"; }
.si-empirekred.si--color::before { color: #72BE50; }
.si-endeavouros::before { content: "\ed54"; }
.si-endeavouros.si--color::before { color: #7F7FFF; }
.si-engadget::before { content: "\ed55"; }
.si-engadget.si--color::before { color: #000000; }
.si-enpass::before { content: "\ed56"; }
.si-enpass.si--color::before { color: #0D47A1; }
.si-enterprisedb::before { content: "\ed57"; }
.si-enterprisedb.si--color::before { color: #FF3E00; }
.si-envato::before { content: "\ed58"; }
.si-envato.si--color::before { color: #81B441; }
.si-envoyproxy::before { content: "\ed59"; }
.si-envoyproxy.si--color::before { color: #AC6199; }
.si-epel::before { content: "\ed5a"; }
.si-epel.si--color::before { color: #FC0000; }
.si-epicgames::before { content: "\ed5b"; }
.si-epicgames.si--color::before { color: #313131; }
.si-epson::before { content: "\ed5c"; }
.si-epson.si--color::before { color: #003399; }
.si-equinixmetal::before { content: "\ed5d"; }
.si-equinixmetal.si--color::before { color: #ED2224; }
.si-eraser::before { content: "\ed5e"; }
.si-eraser.si--color::before { color: #EC2C40; }
.si-ericsson::before { content: "\ed5f"; }
.si-ericsson.si--color::before { color: #0082F0; }
.si-erlang::before { content: "\ed60"; }
.si-erlang.si--color::before { color: #A90533; }
.si-erpnext::before { content: "\ed61"; }
.si-erpnext.si--color::before { color: #0089FF; }
.si-esbuild::before { content: "\ed62"; }
.si-esbuild.si--color::before { color: #FFCF00; }
.si-esea::before { content: "\ed63"; }
.si-esea.si--color::before { color: #0E9648; }
.si-eslgaming::before { content: "\ed64"; }
.si-eslgaming.si--color::before { color: #FFFF09; }
.si-eslint::before { content: "\ed65"; }
.si-eslint.si--color::before { color: #4B32C3; }
.si-esotericsoftware::before { content: "\ed66"; }
.si-esotericsoftware.si--color::before { color: #3FA9F5; }
.si-esphome::before { content: "\ed67"; }
.si-esphome.si--color::before { color: #000000; }
.si-espressif::before { content: "\ed68"; }
.si-espressif.si--color::before { color: #E7352C; }
.si-esri::before { content: "\ed69"; }
.si-esri.si--color::before { color: #000000; }
.si-etcd::before { content: "\ed6a"; }
.si-etcd.si--color::before { color: #419EDA; }
.si-ethereum::before { content: "\ed6b"; }
.si-ethereum.si--color::before { color: #3C3C3D; }
.si-ethers::before { content: "\ed6c"; }
.si-ethers.si--color::before { color: #2535A0; }
.si-ethiopianairlines::before { content: "\ed6d"; }
.si-ethiopianairlines.si--color::before { color: #648B1A; }
.si-etihadairways::before { content: "\ed6e"; }
.si-etihadairways.si--color::before { color: #BD8B13; }
.si-etsy::before { content: "\ed6f"; }
.si-etsy.si--color::before { color: #F16521; }
.si-eventbrite::before { content: "\ed70"; }
.si-eventbrite.si--color::before { color: #F05537; }
.si-eventstore::before { content: "\ed71"; }
.si-eventstore.si--color::before { color: #5AB552; }
.si-evernote::before { content: "\ed72"; }
.si-evernote.si--color::before { color: #00A82D; }
.si-excalidraw::before { content: "\ed73"; }
.si-excalidraw.si--color::before { color: #6965DB; }
.si-exercism::before { content: "\ed74"; }
.si-exercism.si--color::before { color: #009CAB; }
.si-exordo::before { content: "\ed75"; }
.si-exordo.si--color::before { color: #DAA449; }
.si-exoscale::before { content: "\ed76"; }
.si-exoscale.si--color::before { color: #DA291C; }
.si-expedia::before { content: "\ed77"; }
.si-expedia.si--color::before { color: #191E3B; }
.si-expensify::before { content: "\ed78"; }
.si-expensify.si--color::before { color: #0185FF; }
.si-expertsexchange::before { content: "\ed79"; }
.si-expertsexchange.si--color::before { color: #00AAE7; }
.si-expo::before { content: "\ed7a"; }
.si-expo.si--color::before { color: #000020; }
.si-express::before { content: "\ed7b"; }
.si-express.si--color::before { color: #000000; }
.si-expressvpn::before { content: "\ed7c"; }
.si-expressvpn.si--color::before { color: #DA3940; }
.si-eyeem::before { content: "\ed7d"; }
.si-eyeem.si--color::before { color: #000000; }
.si-f1::before { content: "\ed7e"; }
.si-f1.si--color::before { color: #E10600; }
.si-f5::before { content: "\ed7f"; }
.si-f5.si--color::before { color: #E4002B; }
.si-facebook::before { content: "\ed80"; }
.si-facebook.si--color::before { color: #0866FF; }
.si-facebookgaming::before { content: "\ed81"; }
.si-facebookgaming.si--color::before { color: #005FED; }
.si-facebooklive::before { content: "\ed82"; }
.si-facebooklive.si--color::before { color: #ED4242; }
.si-faceit::before { content: "\ed83"; }
.si-faceit.si--color::before { color: #FF5500; }
.si-facepunch::before { content: "\ed84"; }
.si-facepunch.si--color::before { color: #EC1C24; }
.si-fairphone::before { content: "\ed85"; }
.si-fairphone.si--color::before { color: #4495D1; }
.si-falco::before { content: "\ed86"; }
.si-falco.si--color::before { color: #00AEC7; }
.si-falcon::before { content: "\ed87"; }
.si-falcon.si--color::before { color: #F0AD4E; }
.si-fampay::before { content: "\ed88"; }
.si-fampay.si--color::before { color: #FFAD00; }
.si-fandango::before { content: "\ed89"; }
.si-fandango.si--color::before { color: #FF7300; }
.si-fandom::before { content: "\ed8a"; }
.si-fandom.si--color::before { color: #FA005A; }
.si-fanfou::before { content: "\ed8b"; }
.si-fanfou.si--color::before { color: #00CCFF; }
.si-fantom::before { content: "\ed8c"; }
.si-fantom.si--color::before { color: #0928FF; }
.si-farcaster::before { content: "\ed8d"; }
.si-farcaster.si--color::before { color: #855DCD; }
.si-fareharbor::before { content: "\ed8e"; }
.si-fareharbor.si--color::before { color: #0A6ECE; }
.si-farfetch::before { content: "\ed8f"; }
.si-farfetch.si--color::before { color: #000000; }
.si-fastapi::before { content: "\ed90"; }
.si-fastapi.si--color::before { color: #009688; }
.si-fastify::before { content: "\ed91"; }
.si-fastify.si--color::before { color: #000000; }
.si-fastlane::before { content: "\ed92"; }
.si-fastlane.si--color::before { color: #00F200; }
.si-fastly::before { content: "\ed93"; }
.si-fastly.si--color::before { color: #FF282D; }
.si-fathom::before { content: "\ed94"; }
.si-fathom.si--color::before { color: #9187FF; }
.si-fauna::before { content: "\ed95"; }
.si-fauna.si--color::before { color: #3A1AB6; }
.si-favro::before { content: "\ed96"; }
.si-favro.si--color::before { color: #512DA8; }
.si-fdroid::before { content: "\ed97"; }
.si-fdroid.si--color::before { color: #1976D2; }
.si-feathub::before { content: "\ed98"; }
.si-feathub.si--color::before { color: #9B9B9B; }
.si-fedex::before { content: "\ed99"; }
.si-fedex.si--color::before { color: #4D148C; }
.si-fedora::before { content: "\ed9a"; }
.si-fedora.si--color::before { color: #51A2DA; }
.si-feedly::before { content: "\ed9b"; }
.si-feedly.si--color::before { color: #2BB24C; }
.si-ferrari::before { content: "\ed9c"; }
.si-ferrari.si--color::before { color: #D40000; }
.si-ferrarinv::before { content: "\ed9d"; }
.si-ferrarinv.si--color::before { color: #EB2E2C; }
.si-ferretdb::before { content: "\ed9e"; }
.si-ferretdb.si--color::before { color: #042133; }
.si-ffmpeg::before { content: "\ed9f"; }
.si-ffmpeg.si--color::before { color: #007808; }
.si-fi::before { content: "\eda0"; }
.si-fi.si--color::before { color: #00B899; }
.si-fiat::before { content: "\eda1"; }
.si-fiat.si--color::before { color: #941711; }
.si-fidoalliance::before { content: "\eda2"; }
.si-fidoalliance.si--color::before { color: #FFBF3B; }
.si-fifa::before { content: "\eda3"; }
.si-fifa.si--color::before { color: #326295; }
.si-fig::before { content: "\eda4"; }
.si-fig.si--color::before { color: #000000; }
.si-figma::before { content: "\eda5"; }
.si-figma.si--color::before { color: #F24E1E; }
.si-figshare::before { content: "\eda6"; }
.si-figshare.si--color::before { color: #556472; }
.si-fila::before { content: "\eda7"; }
.si-fila.si--color::before { color: #002D62; }
.si-filedotio::before { content: "\eda8"; }
.si-filedotio.si--color::before { color: #3D3C9D; }
.si-files::before { content: "\eda9"; }
.si-files.si--color::before { color: #4285F4; }
.si-filezilla::before { content: "\edaa"; }
.si-filezilla.si--color::before { color: #BF0000; }
.si-fineco::before { content: "\edab"; }
.si-fineco.si--color::before { color: #00549F; }
.si-fing::before { content: "\edac"; }
.si-fing.si--color::before { color: #009AEE; }
.si-firebase::before { content: "\edad"; }
.si-firebase.si--color::before { color: #DD2C00; }
.si-firefish::before { content: "\edae"; }
.si-firefish.si--color::before { color: #F07A5B; }
.si-fireflyiii::before { content: "\edaf"; }
.si-fireflyiii.si--color::before { color: #CD5029; }
.si-firefox::before { content: "\edb0"; }
.si-firefox.si--color::before { color: #FF7139; }
.si-firefoxbrowser::before { content: "\edb1"; }
.si-firefoxbrowser.si--color::before { color: #FF7139; }
.si-fireship::before { content: "\edb2"; }
.si-fireship.si--color::before { color: #EB844E; }
.si-firewalla::before { content: "\edb3"; }
.si-firewalla.si--color::before { color: #C8332D; }
.si-first::before { content: "\edb4"; }
.si-first.si--color::before { color: #0066B3; }
.si-fishshell::before { content: "\edb5"; }
.si-fishshell.si--color::before { color: #34C534; }
.si-fitbit::before { content: "\edb6"; }
.si-fitbit.si--color::before { color: #00B0B9; }
.si-fivem::before { content: "\edb7"; }
.si-fivem.si--color::before { color: #F40552; }
.si-fiverr::before { content: "\edb8"; }
.si-fiverr.si--color::before { color: #1DBF73; }
.si-fizz::before { content: "\edb9"; }
.si-fizz.si--color::before { color: #00D672; }
.si-flashforge::before { content: "\edba"; }
.si-flashforge.si--color::before { color: #000000; }
.si-flask::before { content: "\edbb"; }
.si-flask.si--color::before { color: #000000; }
.si-flat::before { content: "\edbc"; }
.si-flat.si--color::before { color: #3481FE; }
.si-flathub::before { content: "\edbd"; }
.si-flathub.si--color::before { color: #000000; }
.si-flatpak::before { content: "\edbe"; }
.si-flatpak.si--color::before { color: #4A90D9; }
.si-flickr::before { content: "\edbf"; }
.si-flickr.si--color::before { color: #0063DC; }
.si-flightaware::before { content: "\edc0"; }
.si-flightaware.si--color::before { color: #19315B; }
.si-flipboard::before { content: "\edc1"; }
.si-flipboard.si--color::before { color: #E12828; }
.si-flipkart::before { content: "\edc2"; }
.si-flipkart.si--color::before { color: #2874F0; }
.si-floatplane::before { content: "\edc3"; }
.si-floatplane.si--color::before { color: #00AEEF; }
.si-flood::before { content: "\edc4"; }
.si-flood.si--color::before { color: #4285F4; }
.si-fluentbit::before { content: "\edc5"; }
.si-fluentbit.si--color::before { color: #49BDA5; }
.si-fluentd::before { content: "\edc6"; }
.si-fluentd.si--color::before { color: #0E83C8; }
.si-fluke::before { content: "\edc7"; }
.si-fluke.si--color::before { color: #FFC20E; }
.si-flutter::before { content: "\edc8"; }
.si-flutter.si--color::before { color: #02569B; }
.si-flux::before { content: "\edc9"; }
.si-flux.si--color::before { color: #5468FF; }
.si-fluxus::before { content: "\edca"; }
.si-fluxus.si--color::before { color: #FFFFFF; }
.si-flyway::before { content: "\edcb"; }
.si-flyway.si--color::before { color: #CC0200; }
.si-fmod::before { content: "\edcc"; }
.si-fmod.si--color::before { color: #000000; }
.si-fnac::before { content: "\edcd"; }
.si-fnac.si--color::before { color: #E1A925; }
.si-folium::before { content: "\edce"; }
.si-folium.si--color::before { color: #77B829; }
.si-fonoma::before { content: "\edcf"; }
.si-fonoma.si--color::before { color: #02B78F; }
.si-fontawesome::before { content: "\edd0"; }
.si-fontawesome.si--color::before { color: #538DD7; }
.si-fontbase::before { content: "\edd1"; }
.si-fontbase.si--color::before { color: #3D03A7; }
.si-fontforge::before { content: "\edd2"; }
.si-fontforge.si--color::before { color: #F2712B; }
.si-foobar2000::before { content: "\edd3"; }
.si-foobar2000.si--color::before { color: #000000; }
.si-foodpanda::before { content: "\edd4"; }
.si-foodpanda.si--color::before { color: #D70F64; }
.si-ford::before { content: "\edd5"; }
.si-ford.si--color::before { color: #00274E; }
.si-forgejo::before { content: "\edd6"; }
.si-forgejo.si--color::before { color: #FB923C; }
.si-formik::before { content: "\edd7"; }
.si-formik.si--color::before { color: #2563EB; }
.si-formspree::before { content: "\edd8"; }
.si-formspree.si--color::before { color: #E5122E; }
.si-formstack::before { content: "\edd9"; }
.si-formstack.si--color::before { color: #21B573; }
.si-fortinet::before { content: "\edda"; }
.si-fortinet.si--color::before { color: #EE3124; }
.si-fortran::before { content: "\eddb"; }
.si-fortran.si--color::before { color: #734F96; }
.si-fossa::before { content: "\eddc"; }
.si-fossa.si--color::before { color: #289E6D; }
.si-fossilscm::before { content: "\eddd"; }
.si-fossilscm.si--color::before { color: #548294; }
.si-foundryvirtualtabletop::before { content: "\edde"; }
.si-foundryvirtualtabletop.si--color::before { color: #FE6A1F; }
.si-foursquare::before { content: "\eddf"; }
.si-foursquare.si--color::before { color: #3333FF; }
.si-foursquarecityguide::before { content: "\ede0"; }
.si-foursquarecityguide.si--color::before { color: #F94877; }
.si-fox::before { content: "\ede1"; }
.si-fox.si--color::before { color: #000000; }
.si-foxtel::before { content: "\ede2"; }
.si-foxtel.si--color::before { color: #EB5205; }
.si-fozzy::before { content: "\ede3"; }
.si-fozzy.si--color::before { color: #F15B29; }
.si-framer::before { content: "\ede4"; }
.si-framer.si--color::before { color: #0055FF; }
.si-framework::before { content: "\ede5"; }
.si-framework.si--color::before { color: #000000; }
.si-framework7::before { content: "\ede6"; }
.si-framework7.si--color::before { color: #EE350F; }
.si-franprix::before { content: "\ede7"; }
.si-franprix.si--color::before { color: #EC6237; }
.si-frappe::before { content: "\ede8"; }
.si-frappe.si--color::before { color: #0089FF; }
.si-fraunhofergesellschaft::before { content: "\ede9"; }
.si-fraunhofergesellschaft.si--color::before { color: #179C7D; }
.si-freebsd::before { content: "\edea"; }
.si-freebsd.si--color::before { color: #AB2B28; }
.si-freecad::before { content: "\edeb"; }
.si-freecad.si--color::before { color: #729FCF; }
.si-freecodecamp::before { content: "\edec"; }
.si-freecodecamp.si--color::before { color: #0A0A23; }
.si-freedesktopdotorg::before { content: "\eded"; }
.si-freedesktopdotorg.si--color::before { color: #3B80AE; }
.si-freelancer::before { content: "\edee"; }
.si-freelancer.si--color::before { color: #29B2FE; }
.si-freelancermap::before { content: "\edef"; }
.si-freelancermap.si--color::before { color: #00CFD6; }
.si-freenas::before { content: "\edf0"; }
.si-freenas.si--color::before { color: #343434; }
.si-freepik::before { content: "\edf1"; }
.si-freepik.si--color::before { color: #1273EB; }
.si-frontendmentor::before { content: "\edf2"; }
.si-frontendmentor.si--color::before { color: #3F54A3; }
.si-frontify::before { content: "\edf3"; }
.si-frontify.si--color::before { color: #2D3232; }
.si-fsecure::before { content: "\edf4"; }
.si-fsecure.si--color::before { color: #00BAFF; }
.si-fsharp::before { content: "\edf5"; }
.si-fsharp.si--color::before { color: #378BBA; }
.si-fubo::before { content: "\edf6"; }
.si-fubo.si--color::before { color: #C83D1E; }
.si-fueler::before { content: "\edf7"; }
.si-fueler.si--color::before { color: #09C9E3; }
.si-fugacloud::before { content: "\edf8"; }
.si-fugacloud.si--color::before { color: #242F4B; }
.si-fujifilm::before { content: "\edf9"; }
.si-fujifilm.si--color::before { color: #FB0020; }
.si-fujitsu::before { content: "\edfa"; }
.si-fujitsu.si--color::before { color: #FF0000; }
.si-funimation::before { content: "\edfb"; }
.si-funimation.si--color::before { color: #5B0BB5; }
.si-furaffinity::before { content: "\edfc"; }
.si-furaffinity.si--color::before { color: #36566F; }
.si-furrynetwork::before { content: "\edfd"; }
.si-furrynetwork.si--color::before { color: #2E75B4; }
.si-fusionauth::before { content: "\edfe"; }
.si-fusionauth.si--color::before { color: #F58320; }
.si-futurelearn::before { content: "\edff"; }
.si-futurelearn.si--color::before { color: #DE00A5; }
.si-g2::before { content: "\ee00"; }
.si-g2.si--color::before { color: #FF492C; }
.si-g2a::before { content: "\ee01"; }
.si-g2a.si--color::before { color: #F05F00; }
.si-g2g::before { content: "\ee02"; }
.si-g2g.si--color::before { color: #ED1C24; }
.si-galaxus::before { content: "\ee03"; }
.si-galaxus.si--color::before { color: #000000; }
.si-gameandwatch::before { content: "\ee04"; }
.si-gameandwatch.si--color::before { color: #000000; }
.si-gamebanana::before { content: "\ee05"; }
.si-gamebanana.si--color::before { color: #FCEF40; }
.si-gamedeveloper::before { content: "\ee06"; }
.si-gamedeveloper.si--color::before { color: #E60012; }
.si-gamejolt::before { content: "\ee07"; }
.si-gamejolt.si--color::before { color: #CCFF00; }
.si-gameloft::before { content: "\ee08"; }
.si-gameloft.si--color::before { color: #000000; }
.si-gamemaker::before { content: "\ee09"; }
.si-gamemaker.si--color::before { color: #000000; }
.si-garmin::before { content: "\ee0a"; }
.si-garmin.si--color::before { color: #000000; }
.si-gatling::before { content: "\ee0b"; }
.si-gatling.si--color::before { color: #FF9E2A; }
.si-gatsby::before { content: "\ee0c"; }
.si-gatsby.si--color::before { color: #663399; }
.si-gcore::before { content: "\ee0d"; }
.si-gcore.si--color::before { color: #FF4C00; }
.si-gdal::before { content: "\ee0e"; }
.si-gdal.si--color::before { color: #5CAE58; }
.si-geant::before { content: "\ee0f"; }
.si-geant.si--color::before { color: #DD1F26; }
.si-geeksforgeeks::before { content: "\ee10"; }
.si-geeksforgeeks.si--color::before { color: #2F8D46; }
.si-generalelectric::before { content: "\ee11"; }
.si-generalelectric.si--color::before { color: #0870D8; }
.si-generalmotors::before { content: "\ee12"; }
.si-generalmotors.si--color::before { color: #0170CE; }
.si-genius::before { content: "\ee13"; }
.si-genius.si--color::before { color: #FFFF64; }
.si-gentoo::before { content: "\ee14"; }
.si-gentoo.si--color::before { color: #54487A; }
.si-geocaching::before { content: "\ee15"; }
.si-geocaching.si--color::before { color: #00874D; }
.si-geode::before { content: "\ee16"; }
.si-geode.si--color::before { color: #8D7ACF; }
.si-geopandas::before { content: "\ee17"; }
.si-geopandas.si--color::before { color: #139C5A; }
.si-gerrit::before { content: "\ee18"; }
.si-gerrit.si--color::before { color: #EEEEEE; }
.si-getx::before { content: "\ee19"; }
.si-getx.si--color::before { color: #8A2BE2; }
.si-ghost::before { content: "\ee1a"; }
.si-ghost.si--color::before { color: #15171A; }
.si-ghostery::before { content: "\ee1b"; }
.si-ghostery.si--color::before { color: #00AEF0; }
.si-gimp::before { content: "\ee1c"; }
.si-gimp.si--color::before { color: #5C5543; }
.si-gin::before { content: "\ee1d"; }
.si-gin.si--color::before { color: #008ECF; }
.si-giphy::before { content: "\ee1e"; }
.si-giphy.si--color::before { color: #FF6666; }
.si-git::before { content: "\ee1f"; }
.si-git.si--color::before { color: #F05032; }
.si-gitbook::before { content: "\ee20"; }
.si-gitbook.si--color::before { color: #BBDDE5; }
.si-gitconnected::before { content: "\ee21"; }
.si-gitconnected.si--color::before { color: #2E69AE; }
.si-gitea::before { content: "\ee22"; }
.si-gitea.si--color::before { color: #609926; }
.si-gitee::before { content: "\ee23"; }
.si-gitee.si--color::before { color: #C71D23; }
.si-gitextensions::before { content: "\ee24"; }
.si-gitextensions.si--color::before { color: #212121; }
.si-gitforwindows::before { content: "\ee25"; }
.si-gitforwindows.si--color::before { color: #80B3FF; }
.si-github::before { content: "\ee26"; }
.si-github.si--color::before { color: #181717; }
.si-githubactions::before { content: "\ee27"; }
.si-githubactions.si--color::before { color: #2088FF; }
.si-githubcopilot::before { content: "\ee28"; }
.si-githubcopilot.si--color::before { color: #000000; }
.si-githubpages::before { content: "\ee29"; }
.si-githubpages.si--color::before { color: #222222; }
.si-githubsponsors::before { content: "\ee2a"; }
.si-githubsponsors.si--color::before { color: #EA4AAA; }
.si-gitignoredotio::before { content: "\ee2b"; }
.si-gitignoredotio.si--color::before { color: #204ECF; }
.si-gitkraken::before { content: "\ee2c"; }
.si-gitkraken.si--color::before { color: #179287; }
.si-gitlab::before { content: "\ee2d"; }
.si-gitlab.si--color::before { color: #FC6D26; }
.si-gitlfs::before { content: "\ee2e"; }
.si-gitlfs.si--color::before { color: #F64935; }
.si-gitpod::before { content: "\ee2f"; }
.si-gitpod.si--color::before { color: #FFAE33; }
.si-gitter::before { content: "\ee30"; }
.si-gitter.si--color::before { color: #ED1965; }
.si-glassdoor::before { content: "\ee31"; }
.si-glassdoor.si--color::before { color: #00A162; }
.si-glide::before { content: "\ee32"; }
.si-glide.si--color::before { color: #18BED4; }
.si-glitch::before { content: "\ee33"; }
.si-glitch.si--color::before { color: #3333FF; }
.si-globus::before { content: "\ee34"; }
.si-globus.si--color::before { color: #CA6201; }
.si-glovo::before { content: "\ee35"; }
.si-glovo.si--color::before { color: #F2CC38; }
.si-gltf::before { content: "\ee36"; }
.si-gltf.si--color::before { color: #87C540; }
.si-gmail::before { content: "\ee37"; }
.si-gmail.si--color::before { color: #EA4335; }
.si-gnome::before { content: "\ee38"; }
.si-gnome.si--color::before { color: #4A86CF; }
.si-gnometerminal::before { content: "\ee39"; }
.si-gnometerminal.si--color::before { color: #241F31; }
.si-gnu::before { content: "\ee3a"; }
.si-gnu.si--color::before { color: #A42E2B; }
.si-gnubash::before { content: "\ee3b"; }
.si-gnubash.si--color::before { color: #4EAA25; }
.si-gnuemacs::before { content: "\ee3c"; }
.si-gnuemacs.si--color::before { color: #7F5AB6; }
.si-gnuicecat::before { content: "\ee3d"; }
.si-gnuicecat.si--color::before { color: #002F5B; }
.si-gnuprivacyguard::before { content: "\ee3e"; }
.si-gnuprivacyguard.si--color::before { color: #0093DD; }
.si-gnusocial::before { content: "\ee3f"; }
.si-gnusocial.si--color::before { color: #A22430; }
.si-go::before { content: "\ee40"; }
.si-go.si--color::before { color: #00ADD8; }
.si-gocd::before { content: "\ee41"; }
.si-gocd.si--color::before { color: #94399E; }
.si-godaddy::before { content: "\ee42"; }
.si-godaddy.si--color::before { color: #1BDBDB; }
.si-godotengine::before { content: "\ee43"; }
.si-godotengine.si--color::before { color: #478CBF; }
.si-gofundme::before { content: "\ee44"; }
.si-gofundme.si--color::before { color: #00B964; }
.si-gogdotcom::before { content: "\ee45"; }
.si-gogdotcom.si--color::before { color: #86328A; }
.si-gojek::before { content: "\ee46"; }
.si-gojek.si--color::before { color: #00AA13; }
.si-goland::before { content: "\ee47"; }
.si-goland.si--color::before { color: #000000; }
.si-goldenline::before { content: "\ee48"; }
.si-goldenline.si--color::before { color: #FFE005; }
.si-goldmansachs::before { content: "\ee49"; }
.si-goldmansachs.si--color::before { color: #7399C6; }
.si-goodreads::before { content: "\ee4a"; }
.si-goodreads.si--color::before { color: #372213; }
.si-google::before { content: "\ee4b"; }
.si-google.si--color::before { color: #4285F4; }
.si-googleadmob::before { content: "\ee4c"; }
.si-googleadmob.si--color::before { color: #EA4335; }
.si-googleads::before { content: "\ee4d"; }
.si-googleads.si--color::before { color: #4285F4; }
.si-googleadsense::before { content: "\ee4e"; }
.si-googleadsense.si--color::before { color: #4285F4; }
.si-googleanalytics::before { content: "\ee4f"; }
.si-googleanalytics.si--color::before { color: #E37400; }
.si-googleappsscript::before { content: "\ee50"; }
.si-googleappsscript.si--color::before { color: #4285F4; }
.si-googleassistant::before { content: "\ee51"; }
.si-googleassistant.si--color::before { color: #4285F4; }
.si-googleauthenticator::before { content: "\ee52"; }
.si-googleauthenticator.si--color::before { color: #4285F4; }
.si-googlebigquery::before { content: "\ee53"; }
.si-googlebigquery.si--color::before { color: #669DF6; }
.si-googlebigtable::before { content: "\ee54"; }
.si-googlebigtable.si--color::before { color: #669DF6; }
.si-googlecalendar::before { content: "\ee55"; }
.si-googlecalendar.si--color::before { color: #4285F4; }
.si-googlecampaignmanager360::before { content: "\ee56"; }
.si-googlecampaignmanager360.si--color::before { color: #1E8E3E; }
.si-googlecardboard::before { content: "\ee57"; }
.si-googlecardboard.si--color::before { color: #FF7143; }
.si-googlechat::before { content: "\ee58"; }
.si-googlechat.si--color::before { color: #34A853; }
.si-googlechrome::before { content: "\ee59"; }
.si-googlechrome.si--color::before { color: #4285F4; }
.si-googlechronicle::before { content: "\ee5a"; }
.si-googlechronicle.si--color::before { color: #4285F4; }
.si-googleclassroom::before { content: "\ee5b"; }
.si-googleclassroom.si--color::before { color: #0F9D58; }
.si-googlecloud::before { content: "\ee5c"; }
.si-googlecloud.si--color::before { color: #4285F4; }
.si-googlecloudcomposer::before { content: "\ee5d"; }
.si-googlecloudcomposer.si--color::before { color: #4285F4; }
.si-googlecloudspanner::before { content: "\ee5e"; }
.si-googlecloudspanner.si--color::before { color: #4285F4; }
.si-googlecloudstorage::before { content: "\ee5f"; }
.si-googlecloudstorage.si--color::before { color: #AECBFA; }
.si-googlecolab::before { content: "\ee60"; }
.si-googlecolab.si--color::before { color: #F9AB00; }
.si-googlecontaineroptimizedos::before { content: "\ee61"; }
.si-googlecontaineroptimizedos.si--color::before { color: #4285F4; }
.si-googledataflow::before { content: "\ee62"; }
.si-googledataflow.si--color::before { color: #AECBFA; }
.si-googledataproc::before { content: "\ee63"; }
.si-googledataproc.si--color::before { color: #AECBFA; }
.si-googledatastudio::before { content: "\ee64"; }
.si-googledatastudio.si--color::before { color: #669DF6; }
.si-googledisplayandvideo360::before { content: "\ee65"; }
.si-googledisplayandvideo360.si--color::before { color: #34A853; }
.si-googledocs::before { content: "\ee66"; }
.si-googledocs.si--color::before { color: #4285F4; }
.si-googledrive::before { content: "\ee67"; }
.si-googledrive.si--color::before { color: #4285F4; }
.si-googleearth::before { content: "\ee68"; }
.si-googleearth.si--color::before { color: #4285F4; }
.si-googleearthengine::before { content: "\ee69"; }
.si-googleearthengine.si--color::before { color: #4285F4; }
.si-googlefit::before { content: "\ee6a"; }
.si-googlefit.si--color::before { color: #4285F4; }
.si-googlefonts::before { content: "\ee6b"; }
.si-googlefonts.si--color::before { color: #4285F4; }
.si-googleforms::before { content: "\ee6c"; }
.si-googleforms.si--color::before { color: #7248B9; }
.si-googlegemini::before { content: "\ee6d"; }
.si-googlegemini.si--color::before { color: #8E75B2; }
.si-googlehome::before { content: "\ee6e"; }
.si-googlehome.si--color::before { color: #4285F4; }
.si-googlekeep::before { content: "\ee6f"; }
.si-googlekeep.si--color::before { color: #FFBB00; }
.si-googlelens::before { content: "\ee70"; }
.si-googlelens.si--color::before { color: #4285F4; }
.si-googlemaps::before { content: "\ee71"; }
.si-googlemaps.si--color::before { color: #4285F4; }
.si-googlemarketingplatform::before { content: "\ee72"; }
.si-googlemarketingplatform.si--color::before { color: #4285F4; }
.si-googlemeet::before { content: "\ee73"; }
.si-googlemeet.si--color::before { color: #00897B; }
.si-googlemessages::before { content: "\ee74"; }
.si-googlemessages.si--color::before { color: #1A73E8; }
.si-googlenearby::before { content: "\ee75"; }
.si-googlenearby.si--color::before { color: #4285F4; }
.si-googlenews::before { content: "\ee76"; }
.si-googlenews.si--color::before { color: #174EA6; }
.si-googlepay::before { content: "\ee77"; }
.si-googlepay.si--color::before { color: #4285F4; }
.si-googlephotos::before { content: "\ee78"; }
.si-googlephotos.si--color::before { color: #4285F4; }
.si-googleplay::before { content: "\ee79"; }
.si-googleplay.si--color::before { color: #414141; }
.si-googlepubsub::before { content: "\ee7a"; }
.si-googlepubsub.si--color::before { color: #AECBFA; }
.si-googlescholar::before { content: "\ee7b"; }
.si-googlescholar.si--color::before { color: #4285F4; }
.si-googlesearchconsole::before { content: "\ee7c"; }
.si-googlesearchconsole.si--color::before { color: #458CF5; }
.si-googlesheets::before { content: "\ee7d"; }
.si-googlesheets.si--color::before { color: #34A853; }
.si-googleslides::before { content: "\ee7e"; }
.si-googleslides.si--color::before { color: #FBBC04; }
.si-googlestreetview::before { content: "\ee7f"; }
.si-googlestreetview.si--color::before { color: #FEC111; }
.si-googletagmanager::before { content: "\ee80"; }
.si-googletagmanager.si--color::before { color: #246FDB; }
.si-googletasks::before { content: "\ee81"; }
.si-googletasks.si--color::before { color: #2684FC; }
.si-googletranslate::before { content: "\ee82"; }
.si-googletranslate.si--color::before { color: #4285F4; }
.si-gotomeeting::before { content: "\ee83"; }
.si-gotomeeting.si--color::before { color: #F68D2E; }
.si-grab::before { content: "\ee84"; }
.si-grab.si--color::before { color: #00B14F; }
.si-gradle::before { content: "\ee85"; }
.si-gradle.si--color::before { color: #02303A; }
.si-gradleplaypublisher::before { content: "\ee86"; }
.si-gradleplaypublisher.si--color::before { color: #82B816; }
.si-grafana::before { content: "\ee87"; }
.si-grafana.si--color::before { color: #F46800; }
.si-grammarly::before { content: "\ee88"; }
.si-grammarly.si--color::before { color: #027E6F; }
.si-grandfrais::before { content: "\ee89"; }
.si-grandfrais.si--color::before { color: #ED2D2F; }
.si-grapheneos::before { content: "\ee8a"; }
.si-grapheneos.si--color::before { color: #0053A3; }
.si-graphite::before { content: "\ee8b"; }
.si-graphite.si--color::before { color: #000000; }
.si-graphql::before { content: "\ee8c"; }
.si-graphql.si--color::before { color: #E10098; }
.si-grav::before { content: "\ee8d"; }
.si-grav.si--color::before { color: #221E1F; }
.si-gravatar::before { content: "\ee8e"; }
.si-gravatar.si--color::before { color: #1E8CBE; }
.si-graylog::before { content: "\ee8f"; }
.si-graylog.si--color::before { color: #FF3633; }
.si-greasyfork::before { content: "\ee90"; }
.si-greasyfork.si--color::before { color: #670000; }
.si-greatlearning::before { content: "\ee91"; }
.si-greatlearning.si--color::before { color: #0E39A9; }
.si-greenhouse::before { content: "\ee92"; }
.si-greenhouse.si--color::before { color: #24A47F; }
.si-greensock::before { content: "\ee93"; }
.si-greensock.si--color::before { color: #88CE02; }
.si-griddotai::before { content: "\ee94"; }
.si-griddotai.si--color::before { color: #78FF96; }
.si-gridsome::before { content: "\ee95"; }
.si-gridsome.si--color::before { color: #00A672; }
.si-groupme::before { content: "\ee96"; }
.si-groupme.si--color::before { color: #00AFF0; }
.si-groupon::before { content: "\ee97"; }
.si-groupon.si--color::before { color: #53A318; }
.si-grubhub::before { content: "\ee98"; }
.si-grubhub.si--color::before { color: #F63440; }
.si-grunt::before { content: "\ee99"; }
.si-grunt.si--color::before { color: #FAA918; }
.si-gsk::before { content: "\ee9a"; }
.si-gsk.si--color::before { color: #F36633; }
.si-gsmarenadotcom::before { content: "\ee9b"; }
.si-gsmarenadotcom.si--color::before { color: #D50000; }
.si-gstreamer::before { content: "\ee9c"; }
.si-gstreamer.si--color::before { color: #FF3131; }
.si-gtk::before { content: "\ee9d"; }
.si-gtk.si--color::before { color: #7FE719; }
.si-guangzhoumetro::before { content: "\ee9e"; }
.si-guangzhoumetro.si--color::before { color: #C51935; }
.si-guilded::before { content: "\ee9f"; }
.si-guilded.si--color::before { color: #F5C400; }
.si-gulp::before { content: "\eea0"; }
.si-gulp.si--color::before { color: #CF4647; }
.si-gumroad::before { content: "\eea1"; }
.si-gumroad.si--color::before { color: #FF90E8; }
.si-gumtree::before { content: "\eea2"; }
.si-gumtree.si--color::before { color: #72EF36; }
.si-gunicorn::before { content: "\eea3"; }
.si-gunicorn.si--color::before { color: #499848; }
.si-gurobi::before { content: "\eea4"; }
.si-gurobi.si--color::before { color: #EE3524; }
.si-gusto::before { content: "\eea5"; }
.si-gusto.si--color::before { color: #F45D48; }
.si-gutenberg::before { content: "\eea6"; }
.si-gutenberg.si--color::before { color: #000000; }
.si-h3::before { content: "\eea7"; }
.si-h3.si--color::before { color: #1E54B7; }
.si-habr::before { content: "\eea8"; }
.si-habr.si--color::before { color: #65A3BE; }
.si-hackaday::before { content: "\eea9"; }
.si-hackaday.si--color::before { color: #1A1A1A; }
.si-hackclub::before { content: "\eeaa"; }
.si-hackclub.si--color::before { color: #EC3750; }
.si-hackerearth::before { content: "\eeab"; }
.si-hackerearth.si--color::before { color: #2C3454; }
.si-hackernoon::before { content: "\eeac"; }
.si-hackernoon.si--color::before { color: #00FE00; }
.si-hackerone::before { content: "\eead"; }
.si-hackerone.si--color::before { color: #494649; }
.si-hackerrank::before { content: "\eeae"; }
.si-hackerrank.si--color::before { color: #00EA64; }
.si-hackster::before { content: "\eeaf"; }
.si-hackster.si--color::before { color: #2E9FE6; }
.si-hackthebox::before { content: "\eeb0"; }
.si-hackthebox.si--color::before { color: #9FEF00; }
.si-hal::before { content: "\eeb1"; }
.si-hal.si--color::before { color: #B03532; }
.si-handlebarsdotjs::before { content: "\eeb2"; }
.si-handlebarsdotjs.si--color::before { color: #000000; }
.si-handm::before { content: "\eeb3"; }
.si-handm.si--color::before { color: #E50010; }
.si-handshake::before { content: "\eeb4"; }
.si-handshake.si--color::before { color: #D3FB52; }
.si-handshake_protocol::before { content: "\eeb5"; }
.si-handshake_protocol.si--color::before { color: #000000; }
.si-happycow::before { content: "\eeb6"; }
.si-happycow.si--color::before { color: #7C4EC4; }
.si-harbor::before { content: "\eeb7"; }
.si-harbor.si--color::before { color: #60B932; }
.si-harmonyos::before { content: "\eeb8"; }
.si-harmonyos.si--color::before { color: #000000; }
.si-hashicorp::before { content: "\eeb9"; }
.si-hashicorp.si--color::before { color: #000000; }
.si-hashnode::before { content: "\eeba"; }
.si-hashnode.si--color::before { color: #2962FF; }
.si-haskell::before { content: "\eebb"; }
.si-haskell.si--color::before { color: #5D4F85; }
.si-hasura::before { content: "\eebc"; }
.si-hasura.si--color::before { color: #1EB4D4; }
.si-hatenabookmark::before { content: "\eebd"; }
.si-hatenabookmark.si--color::before { color: #00A4DE; }
.si-haveibeenpwned::before { content: "\eebe"; }
.si-haveibeenpwned.si--color::before { color: #2A6379; }
.si-haxe::before { content: "\eebf"; }
.si-haxe.si--color::before { color: #EA8220; }
.si-hbo::before { content: "\eec0"; }
.si-hbo.si--color::before { color: #000000; }
.si-hcl::before { content: "\eec1"; }
.si-hcl.si--color::before { color: #006BB6; }
.si-hdfcbank::before { content: "\eec2"; }
.si-hdfcbank.si--color::before { color: #004B8D; }
.si-headlessui::before { content: "\eec3"; }
.si-headlessui.si--color::before { color: #66E3FF; }
.si-headphonezone::before { content: "\eec4"; }
.si-headphonezone.si--color::before { color: #3C07FF; }
.si-headspace::before { content: "\eec5"; }
.si-headspace.si--color::before { color: #F47D31; }
.si-hearth::before { content: "\eec6"; }
.si-hearth.si--color::before { color: #A33035; }
.si-hearthisdotat::before { content: "\eec7"; }
.si-hearthisdotat.si--color::before { color: #000000; }
.si-hedera::before { content: "\eec8"; }
.si-hedera.si--color::before { color: #222222; }
.si-helium::before { content: "\eec9"; }
.si-helium.si--color::before { color: #0ACF83; }
.si-hellofresh::before { content: "\eeca"; }
.si-hellofresh.si--color::before { color: #99CC33; }
.si-hellyhansen::before { content: "\eecb"; }
.si-hellyhansen.si--color::before { color: #DA2128; }
.si-helm::before { content: "\eecc"; }
.si-helm.si--color::before { color: #0F1689; }
.si-helpdesk::before { content: "\eecd"; }
.si-helpdesk.si--color::before { color: #2FC774; }
.si-helpscout::before { content: "\eece"; }
.si-helpscout.si--color::before { color: #1292EE; }
.si-hepsiemlak::before { content: "\eecf"; }
.si-hepsiemlak.si--color::before { color: #E1251B; }
.si-here::before { content: "\eed0"; }
.si-here.si--color::before { color: #00AFAA; }
.si-heroku::before { content: "\eed1"; }
.si-heroku.si--color::before { color: #430098; }
.si-hetzner::before { content: "\eed2"; }
.si-hetzner.si--color::before { color: #D50C2D; }
.si-hevy::before { content: "\eed3"; }
.si-hevy.si--color::before { color: #000000; }
.si-hexlet::before { content: "\eed4"; }
.si-hexlet.si--color::before { color: #116EF5; }
.si-hexo::before { content: "\eed5"; }
.si-hexo.si--color::before { color: #0E83CD; }
.si-hey::before { content: "\eed6"; }
.si-hey.si--color::before { color: #5522FA; }
.si-hibernate::before { content: "\eed7"; }
.si-hibernate.si--color::before { color: #59666C; }
.si-hibob::before { content: "\eed8"; }
.si-hibob.si--color::before { color: #E42C51; }
.si-hilton::before { content: "\eed9"; }
.si-hilton.si--color::before { color: #231F20; }
.si-hiltonhotelsandresorts::before { content: "\eeda"; }
.si-hiltonhotelsandresorts.si--color::before { color: #1E4380; }
.si-hitachi::before { content: "\eedb"; }
.si-hitachi.si--color::before { color: #E60027; }
.si-hive::before { content: "\eedc"; }
.si-hive.si--color::before { color: #FF7A00; }
.si-hive_blockchain::before { content: "\eedd"; }
.si-hive_blockchain.si--color::before { color: #E31337; }
.si-hivemq::before { content: "\eede"; }
.si-hivemq.si--color::before { color: #FFC000; }
.si-homarr::before { content: "\eedf"; }
.si-homarr.si--color::before { color: #FA5252; }
.si-homeadvisor::before { content: "\eee0"; }
.si-homeadvisor.si--color::before { color: #F68315; }
.si-homeassistant::before { content: "\eee1"; }
.si-homeassistant.si--color::before { color: #18BCF2; }
.si-homeassistantcommunitystore::before { content: "\eee2"; }
.si-homeassistantcommunitystore.si--color::before { color: #41BDF5; }
.si-homebrew::before { content: "\eee3"; }
.si-homebrew.si--color::before { color: #FBB040; }
.si-homebridge::before { content: "\eee4"; }
.si-homebridge.si--color::before { color: #491F59; }
.si-homepage::before { content: "\eee5"; }
.si-homepage.si--color::before { color: #009BD5; }
.si-homify::before { content: "\eee6"; }
.si-homify.si--color::before { color: #7DCDA3; }
.si-honda::before { content: "\eee7"; }
.si-honda.si--color::before { color: #E40521; }
.si-honey::before { content: "\eee8"; }
.si-honey.si--color::before { color: #FF6801; }
.si-hono::before { content: "\eee9"; }
.si-hono.si--color::before { color: #E36002; }
.si-honor::before { content: "\eeea"; }
.si-honor.si--color::before { color: #000000; }
.si-hootsuite::before { content: "\eeeb"; }
.si-hootsuite.si--color::before { color: #FF4C46; }
.si-hoppscotch::before { content: "\eeec"; }
.si-hoppscotch.si--color::before { color: #09090B; }
.si-hostinger::before { content: "\eeed"; }
.si-hostinger.si--color::before { color: #673DE6; }
.si-hotelsdotcom::before { content: "\eeee"; }
.si-hotelsdotcom.si--color::before { color: #EF3346; }
.si-hotjar::before { content: "\eeef"; }
.si-hotjar.si--color::before { color: #FF3C00; }
.si-hotwire::before { content: "\eef0"; }
.si-hotwire.si--color::before { color: #FFE801; }
.si-houdini::before { content: "\eef1"; }
.si-houdini.si--color::before { color: #FF4713; }
.si-houzz::before { content: "\eef2"; }
.si-houzz.si--color::before { color: #4DBC15; }
.si-hp::before { content: "\eef3"; }
.si-hp.si--color::before { color: #0096D6; }
.si-hsbc::before { content: "\eef4"; }
.si-hsbc.si--color::before { color: #DB0011; }
.si-html5::before { content: "\eef5"; }
.si-html5.si--color::before { color: #E34F26; }
.si-htmlacademy::before { content: "\eef6"; }
.si-htmlacademy.si--color::before { color: #302683; }
.si-htmx::before { content: "\eef7"; }
.si-htmx.si--color::before { color: #3366CC; }
.si-htop::before { content: "\eef8"; }
.si-htop.si--color::before { color: #009020; }
.si-httpie::before { content: "\eef9"; }
.si-httpie.si--color::before { color: #73DC8C; }
.si-huawei::before { content: "\eefa"; }
.si-huawei.si--color::before { color: #FF0000; }
.si-hubspot::before { content: "\eefb"; }
.si-hubspot.si--color::before { color: #FF7A59; }
.si-huggingface::before { content: "\eefc"; }
.si-huggingface.si--color::before { color: #FFD21E; }
.si-hugo::before { content: "\eefd"; }
.si-hugo.si--color::before { color: #FF4088; }
.si-humblebundle::before { content: "\eefe"; }
.si-humblebundle.si--color::before { color: #CC2929; }
.si-hungryjacks::before { content: "\eeff"; }
.si-hungryjacks.si--color::before { color: #D0021B; }
.si-husqvarna::before { content: "\ef00"; }
.si-husqvarna.si--color::before { color: #273A60; }
.si-hyper::before { content: "\ef01"; }
.si-hyper.si--color::before { color: #000000; }
.si-hyperskill::before { content: "\ef02"; }
.si-hyperskill.si--color::before { color: #8C5AFF; }
.si-hypothesis::before { content: "\ef03"; }
.si-hypothesis.si--color::before { color: #BD1C2B; }
.si-hyprland::before { content: "\ef04"; }
.si-hyprland.si--color::before { color: #58E1FF; }
.si-hyundai::before { content: "\ef05"; }
.si-hyundai.si--color::before { color: #002C5E; }
.si-i18next::before { content: "\ef06"; }
.si-i18next.si--color::before { color: #26A69A; }
.si-i3::before { content: "\ef07"; }
.si-i3.si--color::before { color: #52C0FF; }
.si-iata::before { content: "\ef08"; }
.si-iata.si--color::before { color: #004E81; }
.si-ibeacon::before { content: "\ef09"; }
.si-ibeacon.si--color::before { color: #3D7EBB; }
.si-iberia::before { content: "\ef0a"; }
.si-iberia.si--color::before { color: #D7192D; }
.si-iced::before { content: "\ef0b"; }
.si-iced.si--color::before { color: #3645FF; }
.si-iceland::before { content: "\ef0c"; }
.si-iceland.si--color::before { color: #CC092F; }
.si-icicibank::before { content: "\ef0d"; }
.si-icicibank.si--color::before { color: #AE282E; }
.si-icinga::before { content: "\ef0e"; }
.si-icinga.si--color::before { color: #06062C; }
.si-icloud::before { content: "\ef0f"; }
.si-icloud.si--color::before { color: #3693F3; }
.si-icomoon::before { content: "\ef10"; }
.si-icomoon.si--color::before { color: #825794; }
.si-icon::before { content: "\ef11"; }
.si-icon.si--color::before { color: #31B8BB; }
.si-iconfinder::before { content: "\ef12"; }
.si-iconfinder.si--color::before { color: #1A1B1F; }
.si-iconify::before { content: "\ef13"; }
.si-iconify.si--color::before { color: #1769AA; }
.si-iconjar::before { content: "\ef14"; }
.si-iconjar.si--color::before { color: #16A5F3; }
.si-icons8::before { content: "\ef15"; }
.si-icons8.si--color::before { color: #1FB141; }
.si-icq::before { content: "\ef16"; }
.si-icq.si--color::before { color: #24FF00; }
.si-ieee::before { content: "\ef17"; }
.si-ieee.si--color::before { color: #00629B; }
.si-ifixit::before { content: "\ef18"; }
.si-ifixit.si--color::before { color: #0071CE; }
.si-ifood::before { content: "\ef19"; }
.si-ifood.si--color::before { color: #EA1D2C; }
.si-ifttt::before { content: "\ef1a"; }
.si-ifttt.si--color::before { color: #000000; }
.si-igdb::before { content: "\ef1b"; }
.si-igdb.si--color::before { color: #9147FF; }
.si-ign::before { content: "\ef1c"; }
.si-ign.si--color::before { color: #BF1313; }
.si-iheartradio::before { content: "\ef1d"; }
.si-iheartradio.si--color::before { color: #C6002B; }
.si-ikea::before { content: "\ef1e"; }
.si-ikea.si--color::before { color: #0058A3; }
.si-iledefrancemobilites::before { content: "\ef1f"; }
.si-iledefrancemobilites.si--color::before { color: #67B4E7; }
.si-imagedotsc::before { content: "\ef20"; }
.si-imagedotsc.si--color::before { color: #039CB2; }
.si-imagej::before { content: "\ef21"; }
.si-imagej.si--color::before { color: #00D8E0; }
.si-imdb::before { content: "\ef22"; }
.si-imdb.si--color::before { color: #F5C518; }
.si-imessage::before { content: "\ef23"; }
.si-imessage.si--color::before { color: #34DA50; }
.si-imgur::before { content: "\ef24"; }
.si-imgur.si--color::before { color: #1BB76E; }
.si-immer::before { content: "\ef25"; }
.si-immer.si--color::before { color: #00E7C3; }
.si-immich::before { content: "\ef26"; }
.si-immich.si--color::before { color: #4250AF; }
.si-imou::before { content: "\ef27"; }
.si-imou.si--color::before { color: #E89313; }
.si-improvmx::before { content: "\ef28"; }
.si-improvmx.si--color::before { color: #2FBEFF; }
.si-indeed::before { content: "\ef29"; }
.si-indeed.si--color::before { color: #003A9B; }
.si-indiansuperleague::before { content: "\ef2a"; }
.si-indiansuperleague.si--color::before { color: #ED2F21; }
.si-indiehackers::before { content: "\ef2b"; }
.si-indiehackers.si--color::before { color: #0E2439; }
.si-indigo::before { content: "\ef2c"; }
.si-indigo.si--color::before { color: #09009B; }
.si-inertia::before { content: "\ef2d"; }
.si-inertia.si--color::before { color: #9553E9; }
.si-infiniti::before { content: "\ef2e"; }
.si-infiniti.si--color::before { color: #020B24; }
.si-influxdb::before { content: "\ef2f"; }
.si-influxdb.si--color::before { color: #22ADF6; }
.si-infomaniak::before { content: "\ef30"; }
.si-infomaniak.si--color::before { color: #0098FF; }
.si-infoq::before { content: "\ef31"; }
.si-infoq.si--color::before { color: #2C6CAF; }
.si-informatica::before { content: "\ef32"; }
.si-informatica.si--color::before { color: #FF4D00; }
.si-infosys::before { content: "\ef33"; }
.si-infosys.si--color::before { color: #007CC3; }
.si-infracost::before { content: "\ef34"; }
.si-infracost.si--color::before { color: #DB44B8; }
.si-ingress::before { content: "\ef35"; }
.si-ingress.si--color::before { color: #783CBD; }
.si-inkdrop::before { content: "\ef36"; }
.si-inkdrop.si--color::before { color: #7A78D7; }
.si-inkscape::before { content: "\ef37"; }
.si-inkscape.si--color::before { color: #000000; }
.si-inoreader::before { content: "\ef38"; }
.si-inoreader.si--color::before { color: #1875F3; }
.si-insomnia::before { content: "\ef39"; }
.si-insomnia.si--color::before { color: #4000BF; }
.si-inspire::before { content: "\ef3a"; }
.si-inspire.si--color::before { color: #00E5FF; }
.si-insta360::before { content: "\ef3b"; }
.si-insta360.si--color::before { color: #FFEE00; }
.si-instacart::before { content: "\ef3c"; }
.si-instacart.si--color::before { color: #43B02A; }
.si-instagram::before { content: "\ef3d"; }
.si-instagram.si--color::before { color: #E4405F; }
.si-instapaper::before { content: "\ef3e"; }
.si-instapaper.si--color::before { color: #1F1F1F; }
.si-instatus::before { content: "\ef3f"; }
.si-instatus.si--color::before { color: #4EE3C2; }
.si-instructables::before { content: "\ef40"; }
.si-instructables.si--color::before { color: #FABF15; }
.si-instructure::before { content: "\ef41"; }
.si-instructure.si--color::before { color: #2A7BA0; }
.si-intel::before { content: "\ef42"; }
.si-intel.si--color::before { color: #0071C5; }
.si-intellijidea::before { content: "\ef43"; }
.si-intellijidea.si--color::before { color: #000000; }
.si-interactiondesignfoundation::before { content: "\ef44"; }
.si-interactiondesignfoundation.si--color::before { color: #2B2B2B; }
.si-interactjs::before { content: "\ef45"; }
.si-interactjs.si--color::before { color: #2599ED; }
.si-interbase::before { content: "\ef46"; }
.si-interbase.si--color::before { color: #E62431; }
.si-intercom::before { content: "\ef47"; }
.si-intercom.si--color::before { color: #6AFDEF; }
.si-intermarche::before { content: "\ef48"; }
.si-intermarche.si--color::before { color: #E2001A; }
.si-internetarchive::before { content: "\ef49"; }
.si-internetarchive.si--color::before { color: #666666; }
.si-internetcomputer::before { content: "\ef4a"; }
.si-internetcomputer.si--color::before { color: #3B00B9; }
.si-intigriti::before { content: "\ef4b"; }
.si-intigriti.si--color::before { color: #161A36; }
.si-intuit::before { content: "\ef4c"; }
.si-intuit.si--color::before { color: #236CFF; }
.si-invision::before { content: "\ef4d"; }
.si-invision.si--color::before { color: #FF3366; }
.si-invoiceninja::before { content: "\ef4e"; }
.si-invoiceninja.si--color::before { color: #000000; }
.si-iobroker::before { content: "\ef4f"; }
.si-iobroker.si--color::before { color: #3399CC; }
.si-ionic::before { content: "\ef50"; }
.si-ionic.si--color::before { color: #3880FF; }
.si-ionos::before { content: "\ef51"; }
.si-ionos.si--color::before { color: #003D8F; }
.si-ios::before { content: "\ef52"; }
.si-ios.si--color::before { color: #000000; }
.si-iota::before { content: "\ef53"; }
.si-iota.si--color::before { color: #131F37; }
.si-ipfs::before { content: "\ef54"; }
.si-ipfs.si--color::before { color: #65C2CB; }
.si-iris::before { content: "\ef55"; }
.si-iris.si--color::before { color: #25313C; }
.si-irobot::before { content: "\ef56"; }
.si-irobot.si--color::before { color: #6CB86A; }
.si-isc2::before { content: "\ef57"; }
.si-isc2.si--color::before { color: #468145; }
.si-issuu::before { content: "\ef58"; }
.si-issuu.si--color::before { color: #F36D5D; }
.si-istio::before { content: "\ef59"; }
.si-istio.si--color::before { color: #466BB0; }
.si-itchdotio::before { content: "\ef5a"; }
.si-itchdotio.si--color::before { color: #FA5C5C; }
.si-iterm2::before { content: "\ef5b"; }
.si-iterm2.si--color::before { color: #000000; }
.si-itunes::before { content: "\ef5c"; }
.si-itunes.si--color::before { color: #FB5BC5; }
.si-itvx::before { content: "\ef5d"; }
.si-itvx.si--color::before { color: #DEEB52; }
.si-iveco::before { content: "\ef5e"; }
.si-iveco.si--color::before { color: #1554FF; }
.si-jabber::before { content: "\ef5f"; }
.si-jabber.si--color::before { color: #CC0000; }
.si-jaeger::before { content: "\ef60"; }
.si-jaeger.si--color::before { color: #66CFE3; }
.si-jaguar::before { content: "\ef61"; }
.si-jaguar.si--color::before { color: #FFFFFF; }
.si-jamboard::before { content: "\ef62"; }
.si-jamboard.si--color::before { color: #F37C20; }
.si-jameson::before { content: "\ef63"; }
.si-jameson.si--color::before { color: #004027; }
.si-jamstack::before { content: "\ef64"; }
.si-jamstack.si--color::before { color: #F0047F; }
.si-jasmine::before { content: "\ef65"; }
.si-jasmine.si--color::before { color: #8A4182; }
.si-javascript::before { content: "\ef66"; }
.si-javascript.si--color::before { color: #F7DF1E; }
.si-jbl::before { content: "\ef67"; }
.si-jbl.si--color::before { color: #FF3300; }
.si-jcb::before { content: "\ef68"; }
.si-jcb.si--color::before { color: #0B4EA2; }
.si-jeep::before { content: "\ef69"; }
.si-jeep.si--color::before { color: #000000; }
.si-jekyll::before { content: "\ef6a"; }
.si-jekyll.si--color::before { color: #CC0000; }
.si-jellyfin::before { content: "\ef6b"; }
.si-jellyfin.si--color::before { color: #00A4DC; }
.si-jenkins::before { content: "\ef6c"; }
.si-jenkins.si--color::before { color: #D24939; }
.si-jest::before { content: "\ef6d"; }
.si-jest.si--color::before { color: #C21325; }
.si-jet::before { content: "\ef6e"; }
.si-jet.si--color::before { color: #FBBA00; }
.si-jetblue::before { content: "\ef6f"; }
.si-jetblue.si--color::before { color: #001E59; }
.si-jetbrains::before { content: "\ef70"; }
.si-jetbrains.si--color::before { color: #000000; }
.si-jetpackcompose::before { content: "\ef71"; }
.si-jetpackcompose.si--color::before { color: #4285F4; }
.si-jfrog::before { content: "\ef72"; }
.si-jfrog.si--color::before { color: #40BE46; }
.si-jfrogpipelines::before { content: "\ef73"; }
.si-jfrogpipelines.si--color::before { color: #40BE46; }
.si-jhipster::before { content: "\ef74"; }
.si-jhipster.si--color::before { color: #3E8ACC; }
.si-jinja::before { content: "\ef75"; }
.si-jinja.si--color::before { color: #B41717; }
.si-jira::before { content: "\ef76"; }
.si-jira.si--color::before { color: #0052CC; }
.si-jirasoftware::before { content: "\ef77"; }
.si-jirasoftware.si--color::before { color: #0052CC; }
.si-jitpack::before { content: "\ef78"; }
.si-jitpack.si--color::before { color: #000000; }
.si-jitsi::before { content: "\ef79"; }
.si-jitsi.si--color::before { color: #97979A; }
.si-johndeere::before { content: "\ef7a"; }
.si-johndeere.si--color::before { color: #367C2B; }
.si-joomla::before { content: "\ef7b"; }
.si-joomla.si--color::before { color: #5091CD; }
.si-joplin::before { content: "\ef7c"; }
.si-joplin.si--color::before { color: #1071D3; }
.si-jordan::before { content: "\ef7d"; }
.si-jordan.si--color::before { color: #000000; }
.si-jouav::before { content: "\ef7e"; }
.si-jouav.si--color::before { color: #E1B133; }
.si-jovian::before { content: "\ef7f"; }
.si-jovian.si--color::before { color: #0D61FF; }
.si-jpeg::before { content: "\ef80"; }
.si-jpeg.si--color::before { color: #8A8A8A; }
.si-jquery::before { content: "\ef81"; }
.si-jquery.si--color::before { color: #0769AD; }
.si-jrgroup::before { content: "\ef82"; }
.si-jrgroup.si--color::before { color: #44AF35; }
.si-jsdelivr::before { content: "\ef83"; }
.si-jsdelivr.si--color::before { color: #E84D3D; }
.si-jsfiddle::before { content: "\ef84"; }
.si-jsfiddle.si--color::before { color: #0084FF; }
.si-json::before { content: "\ef85"; }
.si-json.si--color::before { color: #000000; }
.si-jsonwebtokens::before { content: "\ef86"; }
.si-jsonwebtokens.si--color::before { color: #000000; }
.si-jsr::before { content: "\ef87"; }
.si-jsr.si--color::before { color: #F7DF1E; }
.si-jss::before { content: "\ef88"; }
.si-jss.si--color::before { color: #F7DF1E; }
.si-juce::before { content: "\ef89"; }
.si-juce.si--color::before { color: #8DC63F; }
.si-juejin::before { content: "\ef8a"; }
.si-juejin.si--color::before { color: #007FFF; }
.si-juke::before { content: "\ef8b"; }
.si-juke.si--color::before { color: #6CD74A; }
.si-julia::before { content: "\ef8c"; }
.si-julia.si--color::before { color: #9558B2; }
.si-junipernetworks::before { content: "\ef8d"; }
.si-junipernetworks.si--color::before { color: #84B135; }
.si-junit5::before { content: "\ef8e"; }
.si-junit5.si--color::before { color: #25A162; }
.si-jupyter::before { content: "\ef8f"; }
.si-jupyter.si--color::before { color: #F37626; }
.si-justeat::before { content: "\ef90"; }
.si-justeat.si--color::before { color: #F36D00; }
.si-justgiving::before { content: "\ef91"; }
.si-justgiving.si--color::before { color: #AD29B6; }
.si-k3s::before { content: "\ef92"; }
.si-k3s.si--color::before { color: #FFC61C; }
.si-k6::before { content: "\ef93"; }
.si-k6.si--color::before { color: #7D64FF; }
.si-kaggle::before { content: "\ef94"; }
.si-kaggle.si--color::before { color: #20BEFF; }
.si-kagi::before { content: "\ef95"; }
.si-kagi.si--color::before { color: #FFB319; }
.si-kahoot::before { content: "\ef96"; }
.si-kahoot.si--color::before { color: #46178F; }
.si-kaios::before { content: "\ef97"; }
.si-kaios.si--color::before { color: #6F02B5; }
.si-kakao::before { content: "\ef98"; }
.si-kakao.si--color::before { color: #FFCD00; }
.si-kakaotalk::before { content: "\ef99"; }
.si-kakaotalk.si--color::before { color: #FFCD00; }
.si-kalilinux::before { content: "\ef9a"; }
.si-kalilinux.si--color::before { color: #557C94; }
.si-kamailio::before { content: "\ef9b"; }
.si-kamailio.si--color::before { color: #506365; }
.si-kaniko::before { content: "\ef9c"; }
.si-kaniko.si--color::before { color: #FFA600; }
.si-karlsruherverkehrsverbund::before { content: "\ef9d"; }
.si-karlsruherverkehrsverbund.si--color::before { color: #9B2321; }
.si-kasasmart::before { content: "\ef9e"; }
.si-kasasmart.si--color::before { color: #4ACBD6; }
.si-kashflow::before { content: "\ef9f"; }
.si-kashflow.si--color::before { color: #E5426E; }
.si-kaspersky::before { content: "\efa0"; }
.si-kaspersky.si--color::before { color: #006D5C; }
.si-katacoda::before { content: "\efa1"; }
.si-katacoda.si--color::before { color: #F48220; }
.si-katana::before { content: "\efa2"; }
.si-katana.si--color::before { color: #000000; }
.si-kaufland::before { content: "\efa3"; }
.si-kaufland.si--color::before { color: #E10915; }
.si-kde::before { content: "\efa4"; }
.si-kde.si--color::before { color: #1D99F3; }
.si-kdenlive::before { content: "\efa5"; }
.si-kdenlive.si--color::before { color: #527EB2; }
.si-kedro::before { content: "\efa6"; }
.si-kedro.si--color::before { color: #FFC900; }
.si-keenetic::before { content: "\efa7"; }
.si-keenetic.si--color::before { color: #009EE2; }
.si-keepachangelog::before { content: "\efa8"; }
.si-keepachangelog.si--color::before { color: #E05735; }
.si-keepassxc::before { content: "\efa9"; }
.si-keepassxc.si--color::before { color: #6CAC4D; }
.si-kentico::before { content: "\efaa"; }
.si-kentico.si--color::before { color: #F05A22; }
.si-keras::before { content: "\efab"; }
.si-keras.si--color::before { color: #D00000; }
.si-keybase::before { content: "\efac"; }
.si-keybase.si--color::before { color: #33A0FF; }
.si-keycdn::before { content: "\efad"; }
.si-keycdn.si--color::before { color: #047AED; }
.si-keycloak::before { content: "\efae"; }
.si-keycloak.si--color::before { color: #4D4D4D; }
.si-keystone::before { content: "\efaf"; }
.si-keystone.si--color::before { color: #166BFF; }
.si-kfc::before { content: "\efb0"; }
.si-kfc.si--color::before { color: #F40027; }
.si-khanacademy::before { content: "\efb1"; }
.si-khanacademy.si--color::before { color: #14BF96; }
.si-khronosgroup::before { content: "\efb2"; }
.si-khronosgroup.si--color::before { color: #CC3333; }
.si-kia::before { content: "\efb3"; }
.si-kia.si--color::before { color: #05141F; }
.si-kibana::before { content: "\efb4"; }
.si-kibana.si--color::before { color: #005571; }
.si-kicad::before { content: "\efb5"; }
.si-kicad.si--color::before { color: #314CB0; }
.si-kick::before { content: "\efb6"; }
.si-kick.si--color::before { color: #53FC19; }
.si-kickstarter::before { content: "\efb7"; }
.si-kickstarter.si--color::before { color: #05CE78; }
.si-kik::before { content: "\efb8"; }
.si-kik.si--color::before { color: #82BC23; }
.si-kingstontechnology::before { content: "\efb9"; }
.si-kingstontechnology.si--color::before { color: #000000; }
.si-kinopoisk::before { content: "\efba"; }
.si-kinopoisk.si--color::before { color: #FF5500; }
.si-kinsta::before { content: "\efbb"; }
.si-kinsta.si--color::before { color: #5333ED; }
.si-kirby::before { content: "\efbc"; }
.si-kirby.si--color::before { color: #000000; }
.si-kit::before { content: "\efbd"; }
.si-kit.si--color::before { color: #000000; }
.si-kitsu::before { content: "\efbe"; }
.si-kitsu.si--color::before { color: #FD755C; }
.si-klarna::before { content: "\efbf"; }
.si-klarna.si--color::before { color: #FFB3C7; }
.si-klm::before { content: "\efc0"; }
.si-klm.si--color::before { color: #00A1DE; }
.si-klook::before { content: "\efc1"; }
.si-klook.si--color::before { color: #FF5722; }
.si-knative::before { content: "\efc2"; }
.si-knative.si--color::before { color: #0865AD; }
.si-knexdotjs::before { content: "\efc3"; }
.si-knexdotjs.si--color::before { color: #D26B38; }
.si-knime::before { content: "\efc4"; }
.si-knime.si--color::before { color: #FDD800; }
.si-knip::before { content: "\efc5"; }
.si-knip.si--color::before { color: #F56E0F; }
.si-knowledgebase::before { content: "\efc6"; }
.si-knowledgebase.si--color::before { color: #9146FF; }
.si-known::before { content: "\efc7"; }
.si-known.si--color::before { color: #333333; }
.si-koa::before { content: "\efc8"; }
.si-koa.si--color::before { color: #33333D; }
.si-koc::before { content: "\efc9"; }
.si-koc.si--color::before { color: #F9423A; }
.si-kodak::before { content: "\efca"; }
.si-kodak.si--color::before { color: #ED0000; }
.si-kodi::before { content: "\efcb"; }
.si-kodi.si--color::before { color: #17B2E7; }
.si-koenigsegg::before { content: "\efcc"; }
.si-koenigsegg.si--color::before { color: #000000; }
.si-kofax::before { content: "\efcd"; }
.si-kofax.si--color::before { color: #00558C; }
.si-kofi::before { content: "\efce"; }
.si-kofi.si--color::before { color: #FF5E5B; }
.si-komoot::before { content: "\efcf"; }
.si-komoot.si--color::before { color: #6AA127; }
.si-konami::before { content: "\efd0"; }
.si-konami.si--color::before { color: #B60014; }
.si-kong::before { content: "\efd1"; }
.si-kong.si--color::before { color: #003459; }
.si-kongregate::before { content: "\efd2"; }
.si-kongregate.si--color::before { color: #F04438; }
.si-konva::before { content: "\efd3"; }
.si-konva.si--color::before { color: #0D83CD; }
.si-kotlin::before { content: "\efd4"; }
.si-kotlin.si--color::before { color: #7F52FF; }
.si-koyeb::before { content: "\efd5"; }
.si-koyeb.si--color::before { color: #121212; }
.si-krita::before { content: "\efd6"; }
.si-krita.si--color::before { color: #3BABFF; }
.si-ktm::before { content: "\efd7"; }
.si-ktm.si--color::before { color: #FF6600; }
.si-ktor::before { content: "\efd8"; }
.si-ktor.si--color::before { color: #087CFA; }
.si-kuaishou::before { content: "\efd9"; }
.si-kuaishou.si--color::before { color: #FF4906; }
.si-kubernetes::before { content: "\efda"; }
.si-kubernetes.si--color::before { color: #326CE5; }
.si-kubuntu::before { content: "\efdb"; }
.si-kubuntu.si--color::before { color: #0079C1; }
.si-kucoin::before { content: "\efdc"; }
.si-kucoin.si--color::before { color: #01BC8D; }
.si-kuma::before { content: "\efdd"; }
.si-kuma.si--color::before { color: #290B53; }
.si-kununu::before { content: "\efde"; }
.si-kununu.si--color::before { color: #FFC62E; }
.si-kuula::before { content: "\efdf"; }
.si-kuula.si--color::before { color: #4092B4; }
.si-kx::before { content: "\efe0"; }
.si-kx.si--color::before { color: #101820; }
.si-kyocera::before { content: "\efe1"; }
.si-kyocera.si--color::before { color: #DF0522; }
.si-labview::before { content: "\efe2"; }
.si-labview.si--color::before { color: #FFDB00; }
.si-lada::before { content: "\efe3"; }
.si-lada.si--color::before { color: #ED6B21; }
.si-lamborghini::before { content: "\efe4"; }
.si-lamborghini.si--color::before { color: #B6A272; }
.si-landrover::before { content: "\efe5"; }
.si-landrover.si--color::before { color: #005A2B; }
.si-langchain::before { content: "\efe6"; }
.si-langchain.si--color::before { color: #1C3C3C; }
.si-lapce::before { content: "\efe7"; }
.si-lapce.si--color::before { color: #3B82F6; }
.si-laragon::before { content: "\efe8"; }
.si-laragon.si--color::before { color: #0E83CD; }
.si-laravel::before { content: "\efe9"; }
.si-laravel.si--color::before { color: #FF2D20; }
.si-laravelhorizon::before { content: "\efea"; }
.si-laravelhorizon.si--color::before { color: #405263; }
.si-laravelnova::before { content: "\efeb"; }
.si-laravelnova.si--color::before { color: #252D37; }
.si-lastdotfm::before { content: "\efec"; }
.si-lastdotfm.si--color::before { color: #D51007; }
.si-lastpass::before { content: "\efed"; }
.si-lastpass.si--color::before { color: #D32D27; }
.si-latex::before { content: "\efee"; }
.si-latex.si--color::before { color: #008080; }
.si-launchpad::before { content: "\efef"; }
.si-launchpad.si--color::before { color: #F8C300; }
.si-lazarus::before { content: "\eff0"; }
.si-lazarus.si--color::before { color: #000000; }
.si-lazyvim::before { content: "\eff1"; }
.si-lazyvim.si--color::before { color: #2E7DE9; }
.si-lbry::before { content: "\eff2"; }
.si-lbry.si--color::before { color: #2F9176; }
.si-leaderprice::before { content: "\eff3"; }
.si-leaderprice.si--color::before { color: #E50005; }
.si-leaflet::before { content: "\eff4"; }
.si-leaflet.si--color::before { color: #199900; }
.si-leagueoflegends::before { content: "\eff5"; }
.si-leagueoflegends.si--color::before { color: #C28F2C; }
.si-leanpub::before { content: "\eff6"; }
.si-leanpub.si--color::before { color: #262425; }
.si-leetcode::before { content: "\eff7"; }
.si-leetcode.si--color::before { color: #FFA116; }
.si-legacygames::before { content: "\eff8"; }
.si-legacygames.si--color::before { color: #144B9E; }
.si-leica::before { content: "\eff9"; }
.si-leica.si--color::before { color: #E20612; }
.si-lemmy::before { content: "\effa"; }
.si-lemmy.si--color::before { color: #000000; }
.si-lemonsqueezy::before { content: "\effb"; }
.si-lemonsqueezy.si--color::before { color: #FFC233; }
.si-lenovo::before { content: "\effc"; }
.si-lenovo.si--color::before { color: #E2231A; }
.si-lens::before { content: "\effd"; }
.si-lens.si--color::before { color: #3D90CE; }
.si-leptos::before { content: "\effe"; }
.si-leptos.si--color::before { color: #EF3939; }
.si-lequipe::before { content: "\efff"; }
.si-lequipe.si--color::before { color: #E42829; }
.si-lerna::before { content: "\f000"; }
.si-lerna.si--color::before { color: #9333EA; }
.si-leroymerlin::before { content: "\f001"; }
.si-leroymerlin.si--color::before { color: #78BE20; }
.si-leslibraires::before { content: "\f002"; }
.si-leslibraires.si--color::before { color: #CF4A0C; }
.si-less::before { content: "\f003"; }
.si-less.si--color::before { color: #1D365D; }
.si-letsencrypt::before { content: "\f004"; }
.si-letsencrypt.si--color::before { color: #003A70; }
.si-letterboxd::before { content: "\f005"; }
.si-letterboxd.si--color::before { color: #202830; }
.si-levelsdotfyi::before { content: "\f006"; }
.si-levelsdotfyi.si--color::before { color: #788B95; }
.si-lg::before { content: "\f007"; }
.si-lg.si--color::before { color: #A50034; }
.si-liberadotchat::before { content: "\f008"; }
.si-liberadotchat.si--color::before { color: #FF55DD; }
.si-liberapay::before { content: "\f009"; }
.si-liberapay.si--color::before { color: #F6C915; }
.si-librariesdotio::before { content: "\f00a"; }
.si-librariesdotio.si--color::before { color: #337AB7; }
.si-librarything::before { content: "\f00b"; }
.si-librarything.si--color::before { color: #251A15; }
.si-libreoffice::before { content: "\f00c"; }
.si-libreoffice.si--color::before { color: #18A303; }
.si-libreofficebase::before { content: "\f00d"; }
.si-libreofficebase.si--color::before { color: #7324A9; }
.si-libreofficecalc::before { content: "\f00e"; }
.si-libreofficecalc.si--color::before { color: #007C3C; }
.si-libreofficedraw::before { content: "\f00f"; }
.si-libreofficedraw.si--color::before { color: #CB6D30; }
.si-libreofficeimpress::before { content: "\f010"; }
.si-libreofficeimpress.si--color::before { color: #D0120D; }
.si-libreofficemath::before { content: "\f011"; }
.si-libreofficemath.si--color::before { color: #C10018; }
.si-libreofficewriter::before { content: "\f012"; }
.si-libreofficewriter.si--color::before { color: #083FA6; }
.si-librewolf::before { content: "\f013"; }
.si-librewolf.si--color::before { color: #00ACFF; }
.si-libuv::before { content: "\f014"; }
.si-libuv.si--color::before { color: #403C3D; }
.si-lichess::before { content: "\f015"; }
.si-lichess.si--color::before { color: #000000; }
.si-lidl::before { content: "\f016"; }
.si-lidl.si--color::before { color: #0050AA; }
.si-lifx::before { content: "\f017"; }
.si-lifx.si--color::before { color: #000000; }
.si-lightburn::before { content: "\f018"; }
.si-lightburn.si--color::before { color: #57182D; }
.si-lighthouse::before { content: "\f019"; }
.si-lighthouse.si--color::before { color: #F44B21; }
.si-lightning::before { content: "\f01a"; }
.si-lightning.si--color::before { color: #792EE5; }
.si-limesurvey::before { content: "\f01b"; }
.si-limesurvey.si--color::before { color: #14AE5C; }
.si-line::before { content: "\f01c"; }
.si-line.si--color::before { color: #00C300; }
.si-lineageos::before { content: "\f01d"; }
.si-lineageos.si--color::before { color: #167C80; }
.si-linear::before { content: "\f01e"; }
.si-linear.si--color::before { color: #5E6AD2; }
.si-lining::before { content: "\f01f"; }
.si-lining.si--color::before { color: #C5242C; }
.si-linkedin::before { content: "\f020"; }
.si-linkedin.si--color::before { color: #0A66C2; }
.si-linkerd::before { content: "\f021"; }
.si-linkerd.si--color::before { color: #2BEDA7; }
.si-linkfire::before { content: "\f022"; }
.si-linkfire.si--color::before { color: #FF3850; }
.si-linksys::before { content: "\f023"; }
.si-linksys.si--color::before { color: #000000; }
.si-linktree::before { content: "\f024"; }
.si-linktree.si--color::before { color: #43E55E; }
.si-lintcode::before { content: "\f025"; }
.si-lintcode.si--color::before { color: #13B4FF; }
.si-linux::before { content: "\f026"; }
.si-linux.si--color::before { color: #FCC624; }
.si-linuxcontainers::before { content: "\f027"; }
.si-linuxcontainers.si--color::before { color: #333333; }
.si-linuxfoundation::before { content: "\f028"; }
.si-linuxfoundation.si--color::before { color: #003778; }
.si-linuxmint::before { content: "\f029"; }
.si-linuxmint.si--color::before { color: #86BE43; }
.si-linuxprofessionalinstitute::before { content: "\f02a"; }
.si-linuxprofessionalinstitute.si--color::before { color: #FDC300; }
.si-linuxserver::before { content: "\f02b"; }
.si-linuxserver.si--color::before { color: #DA3B8A; }
.si-lionair::before { content: "\f02c"; }
.si-lionair.si--color::before { color: #ED3237; }
.si-liquibase::before { content: "\f02d"; }
.si-liquibase.si--color::before { color: #2962FF; }
.si-listmonk::before { content: "\f02e"; }
.si-listmonk.si--color::before { color: #0055D4; }
.si-lit::before { content: "\f02f"; }
.si-lit.si--color::before { color: #324FFF; }
.si-litecoin::before { content: "\f030"; }
.si-litecoin.si--color::before { color: #A6A9AA; }
.si-literal::before { content: "\f031"; }
.si-literal.si--color::before { color: #000000; }
.si-litiengine::before { content: "\f032"; }
.si-litiengine.si--color::before { color: #00A5BC; }
.si-livechat::before { content: "\f033"; }
.si-livechat.si--color::before { color: #FF5100; }
.si-livejournal::before { content: "\f034"; }
.si-livejournal.si--color::before { color: #00B0EA; }
.si-livewire::before { content: "\f035"; }
.si-livewire.si--color::before { color: #4E56A6; }
.si-llvm::before { content: "\f036"; }
.si-llvm.si--color::before { color: #262D3A; }
.si-lmms::before { content: "\f037"; }
.si-lmms.si--color::before { color: #10B146; }
.si-lobsters::before { content: "\f038"; }
.si-lobsters.si--color::before { color: #AC130D; }
.si-local::before { content: "\f039"; }
.si-local.si--color::before { color: #51BB7B; }
.si-lodash::before { content: "\f03a"; }
.si-lodash.si--color::before { color: #3492FF; }
.si-logitech::before { content: "\f03b"; }
.si-logitech.si--color::before { color: #00B8FC; }
.si-logitechg::before { content: "\f03c"; }
.si-logitechg.si--color::before { color: #00B8FC; }
.si-logmein::before { content: "\f03d"; }
.si-logmein.si--color::before { color: #45B6F2; }
.si-logseq::before { content: "\f03e"; }
.si-logseq.si--color::before { color: #85C8C8; }
.si-logstash::before { content: "\f03f"; }
.si-logstash.si--color::before { color: #005571; }
.si-looker::before { content: "\f040"; }
.si-looker.si--color::before { color: #4285F4; }
.si-loom::before { content: "\f041"; }
.si-loom.si--color::before { color: #625DF5; }
.si-loop::before { content: "\f042"; }
.si-loop.si--color::before { color: #F29400; }
.si-loopback::before { content: "\f043"; }
.si-loopback.si--color::before { color: #3F5DFF; }
.si-lootcrate::before { content: "\f044"; }
.si-lootcrate.si--color::before { color: #1E1E1E; }
.si-lospec::before { content: "\f045"; }
.si-lospec.si--color::before { color: #EAEAEA; }
.si-lotpolishairlines::before { content: "\f046"; }
.si-lotpolishairlines.si--color::before { color: #11397E; }
.si-ltspice::before { content: "\f047"; }
.si-ltspice.si--color::before { color: #900028; }
.si-lua::before { content: "\f048"; }
.si-lua.si--color::before { color: #2C2D72; }
.si-lubuntu::before { content: "\f049"; }
.si-lubuntu.si--color::before { color: #0068C8; }
.si-lucia::before { content: "\f04a"; }
.si-lucia.si--color::before { color: #5F57FF; }
.si-lucid::before { content: "\f04b"; }
.si-lucid.si--color::before { color: #282C33; }
.si-ludwig::before { content: "\f04c"; }
.si-ludwig.si--color::before { color: #FFFFFF; }
.si-lufthansa::before { content: "\f04d"; }
.si-lufthansa.si--color::before { color: #05164D; }
.si-lumen::before { content: "\f04e"; }
.si-lumen.si--color::before { color: #E74430; }
.si-lunacy::before { content: "\f04f"; }
.si-lunacy.si--color::before { color: #179DE3; }
.si-lutris::before { content: "\f050"; }
.si-lutris.si--color::before { color: #FF9900; }
.si-lydia::before { content: "\f051"; }
.si-lydia.si--color::before { color: #0180FF; }
.si-lyft::before { content: "\f052"; }
.si-lyft.si--color::before { color: #FF00BF; }
.si-maas::before { content: "\f053"; }
.si-maas.si--color::before { color: #E95420; }
.si-macos::before { content: "\f054"; }
.si-macos.si--color::before { color: #000000; }
.si-macpaw::before { content: "\f055"; }
.si-macpaw.si--color::before { color: #000000; }
.si-macys::before { content: "\f056"; }
.si-macys.si--color::before { color: #E21A2C; }
.si-magasinsu::before { content: "\f057"; }
.si-magasinsu.si--color::before { color: #E71B34; }
.si-magento::before { content: "\f058"; }
.si-magento.si--color::before { color: #EE672F; }
.si-magic::before { content: "\f059"; }
.si-magic.si--color::before { color: #6851FF; }
.si-magisk::before { content: "\f05a"; }
.si-magisk.si--color::before { color: #00AF9C; }
.si-mahindra::before { content: "\f05b"; }
.si-mahindra.si--color::before { color: #DD052B; }
.si-mailchimp::before { content: "\f05c"; }
.si-mailchimp.si--color::before { color: #FFE01B; }
.si-maildotcom::before { content: "\f05d"; }
.si-maildotcom.si--color::before { color: #004788; }
.si-maildotru::before { content: "\f05e"; }
.si-maildotru.si--color::before { color: #005FF9; }
.si-mailgun::before { content: "\f05f"; }
.si-mailgun.si--color::before { color: #F06B66; }
.si-mailtrap::before { content: "\f060"; }
.si-mailtrap.si--color::before { color: #22D172; }
.si-mainwp::before { content: "\f061"; }
.si-mainwp.si--color::before { color: #7FB100; }
.si-majorleaguehacking::before { content: "\f062"; }
.si-majorleaguehacking.si--color::before { color: #265A8F; }
.si-make::before { content: "\f063"; }
.si-make.si--color::before { color: #6D00CC; }
.si-makerbot::before { content: "\f064"; }
.si-makerbot.si--color::before { color: #FF1E0D; }
.si-malt::before { content: "\f065"; }
.si-malt.si--color::before { color: #FC5757; }
.si-malwarebytes::before { content: "\f066"; }
.si-malwarebytes.si--color::before { color: #0D3ECC; }
.si-mambaui::before { content: "\f067"; }
.si-mambaui.si--color::before { color: #6D28D9; }
.si-mamp::before { content: "\f068"; }
.si-mamp.si--color::before { color: #02749C; }
.si-man::before { content: "\f069"; }
.si-man.si--color::before { color: #E40045; }
.si-manageiq::before { content: "\f06a"; }
.si-manageiq.si--color::before { color: #EF2929; }
.si-manjaro::before { content: "\f06b"; }
.si-manjaro.si--color::before { color: #35BF5C; }
.si-mantine::before { content: "\f06c"; }
.si-mantine.si--color::before { color: #339AF0; }
.si-mapbox::before { content: "\f06d"; }
.si-mapbox.si--color::before { color: #000000; }
.si-maplibre::before { content: "\f06e"; }
.si-maplibre.si--color::before { color: #396CB2; }
.si-mariadb::before { content: "\f06f"; }
.si-mariadb.si--color::before { color: #003545; }
.si-mariadbfoundation::before { content: "\f070"; }
.si-mariadbfoundation.si--color::before { color: #1F305F; }
.si-markdown::before { content: "\f071"; }
.si-markdown.si--color::before { color: #000000; }
.si-marketo::before { content: "\f072"; }
.si-marketo.si--color::before { color: #5C4C9F; }
.si-marko::before { content: "\f073"; }
.si-marko.si--color::before { color: #2596BE; }
.si-marriott::before { content: "\f074"; }
.si-marriott.si--color::before { color: #A70023; }
.si-marvelapp::before { content: "\f075"; }
.si-marvelapp.si--color::before { color: #1FB6FF; }
.si-maserati::before { content: "\f076"; }
.si-maserati.si--color::before { color: #0C2340; }
.si-mastercard::before { content: "\f077"; }
.si-mastercard.si--color::before { color: #EB001B; }
.si-mastercomfig::before { content: "\f078"; }
.si-mastercomfig.si--color::before { color: #009688; }
.si-mastodon::before { content: "\f079"; }
.si-mastodon.si--color::before { color: #6364FF; }
.si-materialdesign::before { content: "\f07a"; }
.si-materialdesign.si--color::before { color: #757575; }
.si-materialdesignicons::before { content: "\f07b"; }
.si-materialdesignicons.si--color::before { color: #2196F3; }
.si-materialformkdocs::before { content: "\f07c"; }
.si-materialformkdocs.si--color::before { color: #526CFE; }
.si-matillion::before { content: "\f07d"; }
.si-matillion.si--color::before { color: #19E57F; }
.si-matomo::before { content: "\f07e"; }
.si-matomo.si--color::before { color: #3152A0; }
.si-matrix::before { content: "\f07f"; }
.si-matrix.si--color::before { color: #000000; }
.si-matterdotjs::before { content: "\f080"; }
.si-matterdotjs.si--color::before { color: #4B5562; }
.si-mattermost::before { content: "\f081"; }
.si-mattermost.si--color::before { color: #0058CC; }
.si-matternet::before { content: "\f082"; }
.si-matternet.si--color::before { color: #261C29; }
.si-mautic::before { content: "\f083"; }
.si-mautic.si--color::before { color: #4E5E9E; }
.si-max::before { content: "\f084"; }
.si-max.si--color::before { color: #525252; }
.si-maxplanckgesellschaft::before { content: "\f085"; }
.si-maxplanckgesellschaft.si--color::before { color: #006C66; }
.si-maytag::before { content: "\f086"; }
.si-maytag.si--color::before { color: #002E5F; }
.si-mazda::before { content: "\f087"; }
.si-mazda.si--color::before { color: #101010; }
.si-mcafee::before { content: "\f088"; }
.si-mcafee.si--color::before { color: #C01818; }
.si-mcdonalds::before { content: "\f089"; }
.si-mcdonalds.si--color::before { color: #FBC817; }
.si-mclaren::before { content: "\f08a"; }
.si-mclaren.si--color::before { color: #FF0000; }
.si-mdbook::before { content: "\f08b"; }
.si-mdbook.si--color::before { color: #000000; }
.si-mdnwebdocs::before { content: "\f08c"; }
.si-mdnwebdocs.si--color::before { color: #000000; }
.si-mdx::before { content: "\f08d"; }
.si-mdx.si--color::before { color: #1B1F24; }
.si-mediafire::before { content: "\f08e"; }
.si-mediafire.si--color::before { color: #1299F3; }
.si-mediamarkt::before { content: "\f08f"; }
.si-mediamarkt.si--color::before { color: #DF0000; }
.si-mediatek::before { content: "\f090"; }
.si-mediatek.si--color::before { color: #EC9430; }
.si-medibangpaint::before { content: "\f091"; }
.si-medibangpaint.si--color::before { color: #00DBDE; }
.si-medium::before { content: "\f092"; }
.si-medium.si--color::before { color: #000000; }
.si-medusa::before { content: "\f093"; }
.si-medusa.si--color::before { color: #000000; }
.si-meetup::before { content: "\f094"; }
.si-meetup.si--color::before { color: #ED1C40; }
.si-mega::before { content: "\f095"; }
.si-mega.si--color::before { color: #D9272E; }
.si-meilisearch::before { content: "\f096"; }
.si-meilisearch.si--color::before { color: #FF5CAA; }
.si-meituan::before { content: "\f097"; }
.si-meituan.si--color::before { color: #FFD100; }
.si-meizu::before { content: "\f098"; }
.si-meizu.si--color::before { color: #FF4132; }
.si-mendeley::before { content: "\f099"; }
.si-mendeley.si--color::before { color: #9D1620; }
.si-mentorcruise::before { content: "\f09a"; }
.si-mentorcruise.si--color::before { color: #172E59; }
.si-mercadopago::before { content: "\f09b"; }
.si-mercadopago.si--color::before { color: #00B1EA; }
.si-mercedes::before { content: "\f09c"; }
.si-mercedes.si--color::before { color: #242424; }
.si-merck::before { content: "\f09d"; }
.si-merck.si--color::before { color: #007A73; }
.si-mercurial::before { content: "\f09e"; }
.si-mercurial.si--color::before { color: #999999; }
.si-mermaid::before { content: "\f09f"; }
.si-mermaid.si--color::before { color: #FF3670; }
.si-messenger::before { content: "\f0a0"; }
.si-messenger.si--color::before { color: #00B2FF; }
.si-meta::before { content: "\f0a1"; }
.si-meta.si--color::before { color: #0467DF; }
.si-metabase::before { content: "\f0a2"; }
.si-metabase.si--color::before { color: #509EE3; }
.si-metacritic::before { content: "\f0a3"; }
.si-metacritic.si--color::before { color: #000000; }
.si-metafilter::before { content: "\f0a4"; }
.si-metafilter.si--color::before { color: #065A8F; }
.si-metasploit::before { content: "\f0a5"; }
.si-metasploit.si--color::before { color: #2596CD; }
.si-meteor::before { content: "\f0a6"; }
.si-meteor.si--color::before { color: #DE4F4F; }
.si-metro::before { content: "\f0a7"; }
.si-metro.si--color::before { color: #EF4242; }
.si-metrodelaciudaddemexico::before { content: "\f0a8"; }
.si-metrodelaciudaddemexico.si--color::before { color: #F77E1C; }
.si-metrodemadrid::before { content: "\f0a9"; }
.si-metrodemadrid.si--color::before { color: #255E9C; }
.si-metrodeparis::before { content: "\f0aa"; }
.si-metrodeparis.si--color::before { color: #003E95; }
.si-mewe::before { content: "\f0ab"; }
.si-mewe.si--color::before { color: #17377F; }
.si-mg::before { content: "\f0ac"; }
.si-mg.si--color::before { color: #FF0000; }
.si-microbit::before { content: "\f0ad"; }
.si-microbit.si--color::before { color: #00ED00; }
.si-microdotblog::before { content: "\f0ae"; }
.si-microdotblog.si--color::before { color: #FF8800; }
.si-microeditor::before { content: "\f0af"; }
.si-microeditor.si--color::before { color: #2E3192; }
.si-microgenetics::before { content: "\f0b0"; }
.si-microgenetics.si--color::before { color: #FF0000; }
.si-micropython::before { content: "\f0b1"; }
.si-micropython.si--color::before { color: #2B2728; }
.si-microstation::before { content: "\f0b2"; }
.si-microstation.si--color::before { color: #62BB47; }
.si-microstrategy::before { content: "\f0b3"; }
.si-microstrategy.si--color::before { color: #D9232E; }
.si-midi::before { content: "\f0b4"; }
.si-midi.si--color::before { color: #000000; }
.si-migadu::before { content: "\f0b5"; }
.si-migadu.si--color::before { color: #0043CE; }
.si-mikrotik::before { content: "\f0b6"; }
.si-mikrotik.si--color::before { color: #293239; }
.si-milanote::before { content: "\f0b7"; }
.si-milanote.si--color::before { color: #31303A; }
.si-milvus::before { content: "\f0b8"; }
.si-milvus.si--color::before { color: #00A1EA; }
.si-minds::before { content: "\f0b9"; }
.si-minds.si--color::before { color: #FED12F; }
.si-minetest::before { content: "\f0ba"; }
.si-minetest.si--color::before { color: #53AC56; }
.si-mini::before { content: "\f0bb"; }
.si-mini.si--color::before { color: #000000; }
.si-minio::before { content: "\f0bc"; }
.si-minio.si--color::before { color: #C72E49; }
.si-mintlify::before { content: "\f0bd"; }
.si-mintlify.si--color::before { color: #18E299; }
.si-minutemailer::before { content: "\f0be"; }
.si-minutemailer.si--color::before { color: #30B980; }
.si-miraheze::before { content: "\f0bf"; }
.si-miraheze.si--color::before { color: #FFFC00; }
.si-miro::before { content: "\f0c0"; }
.si-miro.si--color::before { color: #050038; }
.si-misskey::before { content: "\f0c1"; }
.si-misskey.si--color::before { color: #A1CA03; }
.si-mitsubishi::before { content: "\f0c2"; }
.si-mitsubishi.si--color::before { color: #E60012; }
.si-mix::before { content: "\f0c3"; }
.si-mix.si--color::before { color: #FF8126; }
.si-mixcloud::before { content: "\f0c4"; }
.si-mixcloud.si--color::before { color: #5000FF; }
.si-mixpanel::before { content: "\f0c5"; }
.si-mixpanel.si--color::before { color: #7856FF; }
.si-mlb::before { content: "\f0c6"; }
.si-mlb.si--color::before { color: #041E42; }
.si-mlflow::before { content: "\f0c7"; }
.si-mlflow.si--color::before { color: #0194E2; }
.si-mobx::before { content: "\f0c8"; }
.si-mobx.si--color::before { color: #FF9955; }
.si-mobxstatetree::before { content: "\f0c9"; }
.si-mobxstatetree.si--color::before { color: #FF7102; }
.si-mocha::before { content: "\f0ca"; }
.si-mocha.si--color::before { color: #8D6748; }
.si-mockserviceworker::before { content: "\f0cb"; }
.si-mockserviceworker.si--color::before { color: #FF6A33; }
.si-modal::before { content: "\f0cc"; }
.si-modal.si--color::before { color: #7FEE64; }
.si-modin::before { content: "\f0cd"; }
.si-modin.si--color::before { color: #001729; }
.si-modrinth::before { content: "\f0ce"; }
.si-modrinth.si--color::before { color: #00AF5C; }
.si-modx::before { content: "\f0cf"; }
.si-modx.si--color::before { color: #102C53; }
.si-moleculer::before { content: "\f0d0"; }
.si-moleculer.si--color::before { color: #3CAFCE; }
.si-momenteo::before { content: "\f0d1"; }
.si-momenteo.si--color::before { color: #5A6AB1; }
.si-monero::before { content: "\f0d2"; }
.si-monero.si--color::before { color: #FF6600; }
.si-moneygram::before { content: "\f0d3"; }
.si-moneygram.si--color::before { color: #FF6600; }
.si-mongodb::before { content: "\f0d4"; }
.si-mongodb.si--color::before { color: #47A248; }
.si-mongoose::before { content: "\f0d5"; }
.si-mongoose.si--color::before { color: #880000; }
.si-mongoosedotws::before { content: "\f0d6"; }
.si-mongoosedotws.si--color::before { color: #F04D35; }
.si-monica::before { content: "\f0d7"; }
.si-monica.si--color::before { color: #2C2B29; }
.si-monkeytie::before { content: "\f0d8"; }
.si-monkeytie.si--color::before { color: #1A52C2; }
.si-monkeytype::before { content: "\f0d9"; }
.si-monkeytype.si--color::before { color: #E2B714; }
.si-monogame::before { content: "\f0da"; }
.si-monogame.si--color::before { color: #E73C00; }
.si-monoprix::before { content: "\f0db"; }
.si-monoprix.si--color::before { color: #FB1911; }
.si-monster::before { content: "\f0dc"; }
.si-monster.si--color::before { color: #6D4C9F; }
.si-monzo::before { content: "\f0dd"; }
.si-monzo.si--color::before { color: #14233C; }
.si-moo::before { content: "\f0de"; }
.si-moo.si--color::before { color: #00945E; }
.si-moodle::before { content: "\f0df"; }
.si-moodle.si--color::before { color: #F98012; }
.si-moonrepo::before { content: "\f0e0"; }
.si-moonrepo.si--color::before { color: #6F53F3; }
.si-moq::before { content: "\f0e1"; }
.si-moq.si--color::before { color: #F4BE00; }
.si-moqups::before { content: "\f0e2"; }
.si-moqups.si--color::before { color: #006BE5; }
.si-morrisons::before { content: "\f0e3"; }
.si-morrisons.si--color::before { color: #007531; }
.si-moscowmetro::before { content: "\f0e4"; }
.si-moscowmetro.si--color::before { color: #D9232E; }
.si-motorola::before { content: "\f0e5"; }
.si-motorola.si--color::before { color: #E1140A; }
.si-mozilla::before { content: "\f0e6"; }
.si-mozilla.si--color::before { color: #000000; }
.si-mpv::before { content: "\f0e7"; }
.si-mpv.si--color::before { color: #691F69; }
.si-mqtt::before { content: "\f0e8"; }
.si-mqtt.si--color::before { color: #660066; }
.si-msi::before { content: "\f0e9"; }
.si-msi.si--color::before { color: #FF0000; }
.si-msibusiness::before { content: "\f0ea"; }
.si-msibusiness.si--color::before { color: #9A8555; }
.si-mta::before { content: "\f0eb"; }
.si-mta.si--color::before { color: #0039A6; }
.si-mtr::before { content: "\f0ec"; }
.si-mtr.si--color::before { color: #AC2E45; }
.si-mubi::before { content: "\f0ed"; }
.si-mubi.si--color::before { color: #000000; }
.si-mui::before { content: "\f0ee"; }
.si-mui.si--color::before { color: #007FFF; }
.si-mulesoft::before { content: "\f0ef"; }
.si-mulesoft.si--color::before { color: #00A0DF; }
.si-muller::before { content: "\f0f0"; }
.si-muller.si--color::before { color: #F46519; }
.si-multisim::before { content: "\f0f1"; }
.si-multisim.si--color::before { color: #57B685; }
.si-mumble::before { content: "\f0f2"; }
.si-mumble.si--color::before { color: #000000; }
.si-muo::before { content: "\f0f3"; }
.si-muo.si--color::before { color: #C60D0D; }
.si-mural::before { content: "\f0f4"; }
.si-mural.si--color::before { color: #FF4B4B; }
.si-musescore::before { content: "\f0f5"; }
.si-musescore.si--color::before { color: #1A70B8; }
.si-musicbrainz::before { content: "\f0f6"; }
.si-musicbrainz.si--color::before { color: #BA478F; }
.si-mxlinux::before { content: "\f0f7"; }
.si-mxlinux.si--color::before { color: #000000; }
.si-myanimelist::before { content: "\f0f8"; }
.si-myanimelist.si--color::before { color: #2E51A2; }
.si-myget::before { content: "\f0f9"; }
.si-myget.si--color::before { color: #0C79CE; }
.si-myob::before { content: "\f0fa"; }
.si-myob.si--color::before { color: #6100A5; }
.si-myspace::before { content: "\f0fb"; }
.si-myspace.si--color::before { color: #030303; }
.si-mysql::before { content: "\f0fc"; }
.si-mysql.si--color::before { color: #4479A1; }
.si-n26::before { content: "\f0fd"; }
.si-n26.si--color::before { color: #48AC98; }
.si-n8n::before { content: "\f0fe"; }
.si-n8n.si--color::before { color: #EA4B71; }
.si-namebase::before { content: "\f0ff"; }
.si-namebase.si--color::before { color: #0068FF; }
.si-namecheap::before { content: "\f100"; }
.si-namecheap.si--color::before { color: #DE3723; }
.si-namemc::before { content: "\f101"; }
.si-namemc.si--color::before { color: #12161A; }
.si-namesilo::before { content: "\f102"; }
.si-namesilo.si--color::before { color: #031B4E; }
.si-namuwiki::before { content: "\f103"; }
.si-namuwiki.si--color::before { color: #008275; }
.si-nano::before { content: "\f104"; }
.si-nano.si--color::before { color: #4A90E2; }
.si-nasa::before { content: "\f105"; }
.si-nasa.si--color::before { color: #E03C31; }
.si-nationalgrid::before { content: "\f106"; }
.si-nationalgrid.si--color::before { color: #00148C; }
.si-nationalrail::before { content: "\f107"; }
.si-nationalrail.si--color::before { color: #003366; }
.si-nativescript::before { content: "\f108"; }
.si-nativescript.si--color::before { color: #65ADF1; }
.si-natsdotio::before { content: "\f109"; }
.si-natsdotio.si--color::before { color: #27AAE1; }
.si-naver::before { content: "\f10a"; }
.si-naver.si--color::before { color: #03C75A; }
.si-nba::before { content: "\f10b"; }
.si-nba.si--color::before { color: #253B73; }
.si-nbb::before { content: "\f10c"; }
.si-nbb.si--color::before { color: #FF7100; }
.si-nbc::before { content: "\f10d"; }
.si-nbc.si--color::before { color: #222222; }
.si-ndr::before { content: "\f10e"; }
.si-ndr.si--color::before { color: #0C1754; }
.si-near::before { content: "\f10f"; }
.si-near.si--color::before { color: #000000; }
.si-nec::before { content: "\f110"; }
.si-nec.si--color::before { color: #1414A0; }
.si-neo4j::before { content: "\f111"; }
.si-neo4j.si--color::before { color: #4581C3; }
.si-neovim::before { content: "\f112"; }
.si-neovim.si--color::before { color: #57A143; }
.si-neptune::before { content: "\f113"; }
.si-neptune.si--color::before { color: #5B69C2; }
.si-nestjs::before { content: "\f114"; }
.si-nestjs.si--color::before { color: #E0234E; }
.si-netapp::before { content: "\f115"; }
.si-netapp.si--color::before { color: #0067C5; }
.si-netbsd::before { content: "\f116"; }
.si-netbsd.si--color::before { color: #FF6600; }
.si-netcup::before { content: "\f117"; }
.si-netcup.si--color::before { color: #056473; }
.si-netdata::before { content: "\f118"; }
.si-netdata.si--color::before { color: #00AB44; }
.si-neteasecloudmusic::before { content: "\f119"; }
.si-neteasecloudmusic.si--color::before { color: #D43C33; }
.si-netflix::before { content: "\f11a"; }
.si-netflix.si--color::before { color: #E50914; }
.si-netgear::before { content: "\f11b"; }
.si-netgear.si--color::before { color: #2C262D; }
.si-netlify::before { content: "\f11c"; }
.si-netlify.si--color::before { color: #00C7B7; }
.si-nette::before { content: "\f11d"; }
.si-nette.si--color::before { color: #3484D2; }
.si-netto::before { content: "\f11e"; }
.si-netto.si--color::before { color: #FFE500; }
.si-neutralinojs::before { content: "\f11f"; }
.si-neutralinojs.si--color::before { color: #F89901; }
.si-newbalance::before { content: "\f120"; }
.si-newbalance.si--color::before { color: #CF0A2C; }
.si-newegg::before { content: "\f121"; }
.si-newegg.si--color::before { color: #E05E00; }
.si-newjapanprowrestling::before { content: "\f122"; }
.si-newjapanprowrestling.si--color::before { color: #FF160B; }
.si-newrelic::before { content: "\f123"; }
.si-newrelic.si--color::before { color: #1CE783; }
.si-newyorktimes::before { content: "\f124"; }
.si-newyorktimes.si--color::before { color: #000000; }
.si-nextbilliondotai::before { content: "\f125"; }
.si-nextbilliondotai.si--color::before { color: #8D5A9E; }
.si-nextcloud::before { content: "\f126"; }
.si-nextcloud.si--color::before { color: #0082C9; }
.si-nextdns::before { content: "\f127"; }
.si-nextdns.si--color::before { color: #007BFF; }
.si-nextdoor::before { content: "\f128"; }
.si-nextdoor.si--color::before { color: #8ED500; }
.si-nextdotjs::before { content: "\f129"; }
.si-nextdotjs.si--color::before { color: #000000; }
.si-nextra::before { content: "\f12a"; }
.si-nextra.si--color::before { color: #000000; }
.si-nextui::before { content: "\f12b"; }
.si-nextui.si--color::before { color: #000000; }
.si-nexusmods::before { content: "\f12c"; }
.si-nexusmods.si--color::before { color: #E6832B; }
.si-nfc::before { content: "\f12d"; }
.si-nfc.si--color::before { color: #002E5F; }
.si-nginx::before { content: "\f12e"; }
.si-nginx.si--color::before { color: #009639; }
.si-nginxproxymanager::before { content: "\f12f"; }
.si-nginxproxymanager.si--color::before { color: #F15833; }
.si-ngrok::before { content: "\f130"; }
.si-ngrok.si--color::before { color: #1F1E37; }
.si-ngrx::before { content: "\f131"; }
.si-ngrx.si--color::before { color: #BA2BD2; }
.si-nhl::before { content: "\f132"; }
.si-nhl.si--color::before { color: #000000; }
.si-nicehash::before { content: "\f133"; }
.si-nicehash.si--color::before { color: #FBC342; }
.si-niconico::before { content: "\f134"; }
.si-niconico.si--color::before { color: #231815; }
.si-nike::before { content: "\f135"; }
.si-nike.si--color::before { color: #111111; }
.si-nikon::before { content: "\f136"; }
.si-nikon.si--color::before { color: #FFE100; }
.si-nim::before { content: "\f137"; }
.si-nim.si--color::before { color: #FFE953; }
.si-nintendo::before { content: "\f138"; }
.si-nintendo.si--color::before { color: #E60012; }
.si-nintendo3ds::before { content: "\f139"; }
.si-nintendo3ds.si--color::before { color: #D12228; }
.si-nintendogamecube::before { content: "\f13a"; }
.si-nintendogamecube.si--color::before { color: #6A5FBB; }
.si-nintendoswitch::before { content: "\f13b"; }
.si-nintendoswitch.si--color::before { color: #E60012; }
.si-nissan::before { content: "\f13c"; }
.si-nissan.si--color::before { color: #C3002F; }
.si-nixos::before { content: "\f13d"; }
.si-nixos.si--color::before { color: #5277C3; }
.si-nodedotjs::before { content: "\f13e"; }
.si-nodedotjs.si--color::before { color: #5FA04E; }
.si-nodemon::before { content: "\f13f"; }
.si-nodemon.si--color::before { color: #76D04B; }
.si-nodered::before { content: "\f140"; }
.si-nodered.si--color::before { color: #8F0000; }
.si-nokia::before { content: "\f141"; }
.si-nokia.si--color::before { color: #005AFF; }
.si-nomad::before { content: "\f142"; }
.si-nomad.si--color::before { color: #00CA8E; }
.si-norco::before { content: "\f143"; }
.si-norco.si--color::before { color: #00FF00; }
.si-nordicsemiconductor::before { content: "\f144"; }
.si-nordicsemiconductor.si--color::before { color: #00A9CE; }
.si-nordvpn::before { content: "\f145"; }
.si-nordvpn.si--color::before { color: #4687FF; }
.si-normalizedotcss::before { content: "\f146"; }
.si-normalizedotcss.si--color::before { color: #E3695F; }
.si-norton::before { content: "\f147"; }
.si-norton.si--color::before { color: #FFE01A; }
.si-norwegian::before { content: "\f148"; }
.si-norwegian.si--color::before { color: #D81939; }
.si-notepadplusplus::before { content: "\f149"; }
.si-notepadplusplus.si--color::before { color: #90E59A; }
.si-notion::before { content: "\f14a"; }
.si-notion.si--color::before { color: #000000; }
.si-notist::before { content: "\f14b"; }
.si-notist.si--color::before { color: #333333; }
.si-nounproject::before { content: "\f14c"; }
.si-nounproject.si--color::before { color: #000000; }
.si-novu::before { content: "\f14d"; }
.si-novu.si--color::before { color: #000000; }
.si-now::before { content: "\f14e"; }
.si-now.si--color::before { color: #001211; }
.si-npm::before { content: "\f14f"; }
.si-npm.si--color::before { color: #CB3837; }
.si-nrwl::before { content: "\f150"; }
.si-nrwl.si--color::before { color: #96D7E8; }
.si-nsis::before { content: "\f151"; }
.si-nsis.si--color::before { color: #01B0F0; }
.si-ntfy::before { content: "\f152"; }
.si-ntfy.si--color::before { color: #317F6F; }
.si-nubank::before { content: "\f153"; }
.si-nubank.si--color::before { color: #820AD1; }
.si-nucleo::before { content: "\f154"; }
.si-nucleo.si--color::before { color: #252B2D; }
.si-nuget::before { content: "\f155"; }
.si-nuget.si--color::before { color: #004880; }
.si-nuke::before { content: "\f156"; }
.si-nuke.si--color::before { color: #000000; }
.si-numba::before { content: "\f157"; }
.si-numba.si--color::before { color: #00A3E0; }
.si-numpy::before { content: "\f158"; }
.si-numpy.si--color::before { color: #013243; }
.si-nunjucks::before { content: "\f159"; }
.si-nunjucks.si--color::before { color: #1C4913; }
.si-nushell::before { content: "\f15a"; }
.si-nushell.si--color::before { color: #4E9A06; }
.si-nutanix::before { content: "\f15b"; }
.si-nutanix.si--color::before { color: #024DA1; }
.si-nuxtdotjs::before { content: "\f15c"; }
.si-nuxtdotjs.si--color::before { color: #00DC82; }
.si-nvidia::before { content: "\f15d"; }
.si-nvidia.si--color::before { color: #76B900; }
.si-nvm::before { content: "\f15e"; }
.si-nvm.si--color::before { color: #F4DD4B; }
.si-nx::before { content: "\f15f"; }
.si-nx.si--color::before { color: #143055; }
.si-nxp::before { content: "\f160"; }
.si-nxp.si--color::before { color: #000000; }
.si-nzxt::before { content: "\f161"; }
.si-nzxt.si--color::before { color: #000000; }
.si-observable::before { content: "\f162"; }
.si-observable.si--color::before { color: #353E58; }
.si-obsidian::before { content: "\f163"; }
.si-obsidian.si--color::before { color: #7C3AED; }
.si-obsstudio::before { content: "\f164"; }
.si-obsstudio.si--color::before { color: #302E31; }
.si-ocaml::before { content: "\f165"; }
.si-ocaml.si--color::before { color: #EC6813; }
.si-oclif::before { content: "\f166"; }
.si-oclif.si--color::before { color: #000000; }
.si-octanerender::before { content: "\f167"; }
.si-octanerender.si--color::before { color: #000000; }
.si-octave::before { content: "\f168"; }
.si-octave.si--color::before { color: #0790C0; }
.si-octobercms::before { content: "\f169"; }
.si-octobercms.si--color::before { color: #DB6A26; }
.si-octoprint::before { content: "\f16a"; }
.si-octoprint.si--color::before { color: #13C100; }
.si-octopusdeploy::before { content: "\f16b"; }
.si-octopusdeploy.si--color::before { color: #2F93E0; }
.si-oculus::before { content: "\f16c"; }
.si-oculus.si--color::before { color: #1C1E20; }
.si-odnoklassniki::before { content: "\f16d"; }
.si-odnoklassniki.si--color::before { color: #EE8208; }
.si-odoo::before { content: "\f16e"; }
.si-odoo.si--color::before { color: #714B67; }
.si-odysee::before { content: "\f16f"; }
.si-odysee.si--color::before { color: #EF1970; }
.si-ohdear::before { content: "\f170"; }
.si-ohdear.si--color::before { color: #FF3900; }
.si-okcupid::before { content: "\f171"; }
.si-okcupid.si--color::before { color: #0500BE; }
.si-okta::before { content: "\f172"; }
.si-okta.si--color::before { color: #007DC1; }
.si-oneplus::before { content: "\f173"; }
.si-oneplus.si--color::before { color: #F5010C; }
.si-onlyfans::before { content: "\f174"; }
.si-onlyfans.si--color::before { color: #00AFF0; }
.si-onlyoffice::before { content: "\f175"; }
.si-onlyoffice.si--color::before { color: #444444; }
.si-onnx::before { content: "\f176"; }
.si-onnx.si--color::before { color: #005CED; }
.si-onstar::before { content: "\f177"; }
.si-onstar.si--color::before { color: #003D7D; }
.si-opel::before { content: "\f178"; }
.si-opel.si--color::before { color: #F7FF14; }
.si-openaccess::before { content: "\f179"; }
.si-openaccess.si--color::before { color: #F68212; }
.si-openai::before { content: "\f17a"; }
.si-openai.si--color::before { color: #412991; }
.si-openaigym::before { content: "\f17b"; }
.si-openaigym.si--color::before { color: #0081A5; }
.si-openapiinitiative::before { content: "\f17c"; }
.si-openapiinitiative.si--color::before { color: #6BA539; }
.si-openbadges::before { content: "\f17d"; }
.si-openbadges.si--color::before { color: #073B5A; }
.si-openbsd::before { content: "\f17e"; }
.si-openbsd.si--color::before { color: #F2CA30; }
.si-openbugbounty::before { content: "\f17f"; }
.si-openbugbounty.si--color::before { color: #F67909; }
.si-opencollective::before { content: "\f180"; }
.si-opencollective.si--color::before { color: #7FADF2; }
.si-opencontainersinitiative::before { content: "\f181"; }
.si-opencontainersinitiative.si--color::before { color: #262261; }
.si-opencv::before { content: "\f182"; }
.si-opencv.si--color::before { color: #5C3EE8; }
.si-openfaas::before { content: "\f183"; }
.si-openfaas.si--color::before { color: #3B5EE9; }
.si-opengl::before { content: "\f184"; }
.si-opengl.si--color::before { color: #5586A4; }
.si-openhab::before { content: "\f185"; }
.si-openhab.si--color::before { color: #E64A19; }
.si-openid::before { content: "\f186"; }
.si-openid.si--color::before { color: #F78C40; }
.si-openjdk::before { content: "\f187"; }
.si-openjdk.si--color::before { color: #000000; }
.si-openjsfoundation::before { content: "\f188"; }
.si-openjsfoundation.si--color::before { color: #0075C9; }
.si-openlayers::before { content: "\f189"; }
.si-openlayers.si--color::before { color: #1F6B75; }
.si-openmediavault::before { content: "\f18a"; }
.si-openmediavault.si--color::before { color: #5DACDF; }
.si-openmined::before { content: "\f18b"; }
.si-openmined.si--color::before { color: #ED986C; }
.si-opennebula::before { content: "\f18c"; }
.si-opennebula.si--color::before { color: #0097C2; }
.si-openproject::before { content: "\f18d"; }
.si-openproject.si--color::before { color: #0770B8; }
.si-openscad::before { content: "\f18e"; }
.si-openscad.si--color::before { color: #F9D72C; }
.si-opensea::before { content: "\f18f"; }
.si-opensea.si--color::before { color: #2081E2; }
.si-opensearch::before { content: "\f190"; }
.si-opensearch.si--color::before { color: #005EB8; }
.si-opensourcehardware::before { content: "\f191"; }
.si-opensourcehardware.si--color::before { color: #0099B0; }
.si-opensourceinitiative::before { content: "\f192"; }
.si-opensourceinitiative.si--color::before { color: #3DA639; }
.si-openssl::before { content: "\f193"; }
.si-openssl.si--color::before { color: #721412; }
.si-openstack::before { content: "\f194"; }
.si-openstack.si--color::before { color: #ED1944; }
.si-openstreetmap::before { content: "\f195"; }
.si-openstreetmap.si--color::before { color: #7EBC6F; }
.si-opensuse::before { content: "\f196"; }
.si-opensuse.si--color::before { color: #73BA25; }
.si-opentelemetry::before { content: "\f197"; }
.si-opentelemetry.si--color::before { color: #000000; }
.si-opentext::before { content: "\f198"; }
.si-opentext.si--color::before { color: #000000; }
.si-opentofu::before { content: "\f199"; }
.si-opentofu.si--color::before { color: #FFDA18; }
.si-openverse::before { content: "\f19a"; }
.si-openverse.si--color::before { color: #FFE033; }
.si-openvpn::before { content: "\f19b"; }
.si-openvpn.si--color::before { color: #EA7E20; }
.si-openwrt::before { content: "\f19c"; }
.si-openwrt.si--color::before { color: #00B5E2; }
.si-openzeppelin::before { content: "\f19d"; }
.si-openzeppelin.si--color::before { color: #4E5EE4; }
.si-openzfs::before { content: "\f19e"; }
.si-openzfs.si--color::before { color: #2A667F; }
.si-opera::before { content: "\f19f"; }
.si-opera.si--color::before { color: #FF1B2D; }
.si-operagx::before { content: "\f1a0"; }
.si-operagx.si--color::before { color: #EE2950; }
.si-opnsense::before { content: "\f1a1"; }
.si-opnsense.si--color::before { color: #D94F00; }
.si-oppo::before { content: "\f1a2"; }
.si-oppo.si--color::before { color: #2D683D; }
.si-opsgenie::before { content: "\f1a3"; }
.si-opsgenie.si--color::before { color: #172B4D; }
.si-opslevel::before { content: "\f1a4"; }
.si-opslevel.si--color::before { color: #0A53E0; }
.si-optimism::before { content: "\f1a5"; }
.si-optimism.si--color::before { color: #FF0420; }
.si-oracle::before { content: "\f1a6"; }
.si-oracle.si--color::before { color: #F80000; }
.si-orange::before { content: "\f1a7"; }
.si-orange.si--color::before { color: #FF7900; }
.si-orcid::before { content: "\f1a8"; }
.si-orcid.si--color::before { color: #A6CE39; }
.si-oreilly::before { content: "\f1a9"; }
.si-oreilly.si--color::before { color: #D3002D; }
.si-org::before { content: "\f1aa"; }
.si-org.si--color::before { color: #77AA99; }
.si-organicmaps::before { content: "\f1ab"; }
.si-organicmaps.si--color::before { color: #006C35; }
.si-origin::before { content: "\f1ac"; }
.si-origin.si--color::before { color: #F56C2D; }
.si-osano::before { content: "\f1ad"; }
.si-osano.si--color::before { color: #7764FA; }
.si-osf::before { content: "\f1ae"; }
.si-osf.si--color::before { color: #2CB9F1; }
.si-osgeo::before { content: "\f1af"; }
.si-osgeo.si--color::before { color: #4CB05B; }
.si-oshkosh::before { content: "\f1b0"; }
.si-oshkosh.si--color::before { color: #E6830F; }
.si-osmc::before { content: "\f1b1"; }
.si-osmc.si--color::before { color: #17394A; }
.si-osu::before { content: "\f1b2"; }
.si-osu.si--color::before { color: #FF66AA; }
.si-otto::before { content: "\f1b3"; }
.si-otto.si--color::before { color: #D4021D; }
.si-outline::before { content: "\f1b4"; }
.si-outline.si--color::before { color: #000000; }
.si-overcast::before { content: "\f1b5"; }
.si-overcast.si--color::before { color: #FC7E0F; }
.si-overleaf::before { content: "\f1b6"; }
.si-overleaf.si--color::before { color: #47A141; }
.si-ovh::before { content: "\f1b7"; }
.si-ovh.si--color::before { color: #123F6D; }
.si-owasp::before { content: "\f1b8"; }
.si-owasp.si--color::before { color: #000000; }
.si-owncloud::before { content: "\f1b9"; }
.si-owncloud.si--color::before { color: #041E42; }
.si-oxygen::before { content: "\f1ba"; }
.si-oxygen.si--color::before { color: #3A209E; }
.si-oyo::before { content: "\f1bb"; }
.si-oyo.si--color::before { color: #EE2E24; }
.si-p5dotjs::before { content: "\f1bc"; }
.si-p5dotjs.si--color::before { color: #ED225D; }
.si-packagist::before { content: "\f1bd"; }
.si-packagist.si--color::before { color: #F28D1A; }
.si-packer::before { content: "\f1be"; }
.si-packer.si--color::before { color: #02A8EF; }
.si-packt::before { content: "\f1bf"; }
.si-packt.si--color::before { color: #F37143; }
.si-paddle::before { content: "\f1c0"; }
.si-paddle.si--color::before { color: #FDDD35; }
.si-paddlepaddle::before { content: "\f1c1"; }
.si-paddlepaddle.si--color::before { color: #0062B0; }
.si-paddypower::before { content: "\f1c2"; }
.si-paddypower.si--color::before { color: #004833; }
.si-pagekit::before { content: "\f1c3"; }
.si-pagekit.si--color::before { color: #212121; }
.si-pagerduty::before { content: "\f1c4"; }
.si-pagerduty.si--color::before { color: #06AC38; }
.si-pagespeedinsights::before { content: "\f1c5"; }
.si-pagespeedinsights.si--color::before { color: #4285F4; }
.si-pagseguro::before { content: "\f1c6"; }
.si-pagseguro.si--color::before { color: #FFC801; }
.si-palantir::before { content: "\f1c7"; }
.si-palantir.si--color::before { color: #101113; }
.si-paloaltonetworks::before { content: "\f1c8"; }
.si-paloaltonetworks.si--color::before { color: #F04E23; }
.si-paloaltosoftware::before { content: "\f1c9"; }
.si-paloaltosoftware.si--color::before { color: #83DA77; }
.si-panasonic::before { content: "\f1ca"; }
.si-panasonic.si--color::before { color: #0049AB; }
.si-pandas::before { content: "\f1cb"; }
.si-pandas.si--color::before { color: #150458; }
.si-pandora::before { content: "\f1cc"; }
.si-pandora.si--color::before { color: #224099; }
.si-pantheon::before { content: "\f1cd"; }
.si-pantheon.si--color::before { color: #FFDC28; }
.si-paperlessngx::before { content: "\f1ce"; }
.si-paperlessngx.si--color::before { color: #17541F; }
.si-paperspace::before { content: "\f1cf"; }
.si-paperspace.si--color::before { color: #000000; }
.si-paperswithcode::before { content: "\f1d0"; }
.si-paperswithcode.si--color::before { color: #21CBCE; }
.si-paramountplus::before { content: "\f1d1"; }
.si-paramountplus.si--color::before { color: #0064FF; }
.si-paritysubstrate::before { content: "\f1d2"; }
.si-paritysubstrate.si--color::before { color: #282828; }
.si-parrotsecurity::before { content: "\f1d3"; }
.si-parrotsecurity.si--color::before { color: #15E0ED; }
.si-parsedotly::before { content: "\f1d4"; }
.si-parsedotly.si--color::before { color: #5BA745; }
.si-passport::before { content: "\f1d5"; }
.si-passport.si--color::before { color: #34E27A; }
.si-pastebin::before { content: "\f1d6"; }
.si-pastebin.si--color::before { color: #02456C; }
.si-patreon::before { content: "\f1d7"; }
.si-patreon.si--color::before { color: #000000; }
.si-paychex::before { content: "\f1d8"; }
.si-paychex.si--color::before { color: #004B8D; }
.si-payhip::before { content: "\f1d9"; }
.si-payhip.si--color::before { color: #5C6AC4; }
.si-payloadcms::before { content: "\f1da"; }
.si-payloadcms.si--color::before { color: #000000; }
.si-payoneer::before { content: "\f1db"; }
.si-payoneer.si--color::before { color: #FF4800; }
.si-paypal::before { content: "\f1dc"; }
.si-paypal.si--color::before { color: #003087; }
.si-paytm::before { content: "\f1dd"; }
.si-paytm.si--color::before { color: #20336B; }
.si-pcgamingwiki::before { content: "\f1de"; }
.si-pcgamingwiki.si--color::before { color: #556DB3; }
.si-pdm::before { content: "\f1df"; }
.si-pdm.si--color::before { color: #AC75D7; }
.si-pdq::before { content: "\f1e0"; }
.si-pdq.si--color::before { color: #231F20; }
.si-peakdesign::before { content: "\f1e1"; }
.si-peakdesign.si--color::before { color: #1C1B1C; }
.si-pearson::before { content: "\f1e2"; }
.si-pearson.si--color::before { color: #000000; }
.si-peerlist::before { content: "\f1e3"; }
.si-peerlist.si--color::before { color: #00AA45; }
.si-peertube::before { content: "\f1e4"; }
.si-peertube.si--color::before { color: #F1680D; }
.si-pegasusairlines::before { content: "\f1e5"; }
.si-pegasusairlines.si--color::before { color: #FDC43E; }
.si-pelican::before { content: "\f1e6"; }
.si-pelican.si--color::before { color: #14A0C4; }
.si-peloton::before { content: "\f1e7"; }
.si-peloton.si--color::before { color: #181A1D; }
.si-penny::before { content: "\f1e8"; }
.si-penny.si--color::before { color: #CD1414; }
.si-penpot::before { content: "\f1e9"; }
.si-penpot.si--color::before { color: #000000; }
.si-percy::before { content: "\f1ea"; }
.si-percy.si--color::before { color: #9E66BF; }
.si-perforce::before { content: "\f1eb"; }
.si-perforce.si--color::before { color: #404040; }
.si-perl::before { content: "\f1ec"; }
.si-perl.si--color::before { color: #39457E; }
.si-perplexity::before { content: "\f1ed"; }
.si-perplexity.si--color::before { color: #1FB8CD; }
.si-persistent::before { content: "\f1ee"; }
.si-persistent.si--color::before { color: #FD5F07; }
.si-personio::before { content: "\f1ef"; }
.si-personio.si--color::before { color: #000000; }
.si-petsathome::before { content: "\f1f0"; }
.si-petsathome.si--color::before { color: #00AA28; }
.si-peugeot::before { content: "\f1f1"; }
.si-peugeot.si--color::before { color: #000000; }
.si-pexels::before { content: "\f1f2"; }
.si-pexels.si--color::before { color: #05A081; }
.si-pfsense::before { content: "\f1f3"; }
.si-pfsense.si--color::before { color: #212121; }
.si-phabricator::before { content: "\f1f4"; }
.si-phabricator.si--color::before { color: #4A5F88; }
.si-philipshue::before { content: "\f1f5"; }
.si-philipshue.si--color::before { color: #0065D3; }
.si-phoenixframework::before { content: "\f1f6"; }
.si-phoenixframework.si--color::before { color: #FD4F00; }
.si-phonepe::before { content: "\f1f7"; }
.si-phonepe.si--color::before { color: #5F259F; }
.si-phosphoricons::before { content: "\f1f8"; }
.si-phosphoricons.si--color::before { color: #3C402B; }
.si-photobucket::before { content: "\f1f9"; }
.si-photobucket.si--color::before { color: #1C47CB; }
.si-photocrowd::before { content: "\f1fa"; }
.si-photocrowd.si--color::before { color: #3DAD4B; }
.si-photon::before { content: "\f1fb"; }
.si-photon.si--color::before { color: #004480; }
.si-photopea::before { content: "\f1fc"; }
.si-photopea.si--color::before { color: #18A497; }
.si-php::before { content: "\f1fd"; }
.si-php.si--color::before { color: #777BB4; }
.si-phpmyadmin::before { content: "\f1fe"; }
.si-phpmyadmin.si--color::before { color: #6C78AF; }
.si-phpstorm::before { content: "\f1ff"; }
.si-phpstorm.si--color::before { color: #000000; }
.si-piaggiogroup::before { content: "\f200"; }
.si-piaggiogroup.si--color::before { color: #000000; }
.si-piapro::before { content: "\f201"; }
.si-piapro.si--color::before { color: #E4007B; }
.si-picardsurgeles::before { content: "\f202"; }
.si-picardsurgeles.si--color::before { color: #2D4999; }
.si-picartodottv::before { content: "\f203"; }
.si-picartodottv.si--color::before { color: #1DA456; }
.si-picnic::before { content: "\f204"; }
.si-picnic.si--color::before { color: #E1171E; }
.si-picpay::before { content: "\f205"; }
.si-picpay.si--color::before { color: #21C25E; }
.si-picrew::before { content: "\f206"; }
.si-picrew.si--color::before { color: #FFBD16; }
.si-picxy::before { content: "\f207"; }
.si-picxy.si--color::before { color: #2E3192; }
.si-pihole::before { content: "\f208"; }
.si-pihole.si--color::before { color: #96060C; }
.si-pimcore::before { content: "\f209"; }
.si-pimcore.si--color::before { color: #6428B4; }
.si-pinboard::before { content: "\f20a"; }
.si-pinboard.si--color::before { color: #0000FF; }
.si-pinescript::before { content: "\f20b"; }
.si-pinescript.si--color::before { color: #00B453; }
.si-pinetwork::before { content: "\f20c"; }
.si-pinetwork.si--color::before { color: #F4AF47; }
.si-pingdom::before { content: "\f20d"; }
.si-pingdom.si--color::before { color: #FFF000; }
.si-pino::before { content: "\f20e"; }
.si-pino.si--color::before { color: #687634; }
.si-pinterest::before { content: "\f20f"; }
.si-pinterest.si--color::before { color: #BD081C; }
.si-pioneerdj::before { content: "\f210"; }
.si-pioneerdj.si--color::before { color: #1A1928; }
.si-piped::before { content: "\f211"; }
.si-piped.si--color::before { color: #F84330; }
.si-pipx::before { content: "\f212"; }
.si-pipx.si--color::before { color: #2CFFAA; }
.si-pivotaltracker::before { content: "\f213"; }
.si-pivotaltracker.si--color::before { color: #517A9E; }
.si-piwigo::before { content: "\f214"; }
.si-piwigo.si--color::before { color: #FF7700; }
.si-pix::before { content: "\f215"; }
.si-pix.si--color::before { color: #77B6A8; }
.si-pixabay::before { content: "\f216"; }
.si-pixabay.si--color::before { color: #2EC66D; }
.si-pixelfed::before { content: "\f217"; }
.si-pixelfed.si--color::before { color: #6366F1; }
.si-pixiv::before { content: "\f218"; }
.si-pixiv.si--color::before { color: #0096FA; }
.si-pixlr::before { content: "\f219"; }
.si-pixlr.si--color::before { color: #3EBBDF; }
.si-pkgsrc::before { content: "\f21a"; }
.si-pkgsrc.si--color::before { color: #FF6600; }
.si-planet::before { content: "\f21b"; }
.si-planet.si--color::before { color: #009DB1; }
.si-planetscale::before { content: "\f21c"; }
.si-planetscale.si--color::before { color: #000000; }
.si-plangrid::before { content: "\f21d"; }
.si-plangrid.si--color::before { color: #0085DE; }
.si-platformdotsh::before { content: "\f21e"; }
.si-platformdotsh.si--color::before { color: #1A182A; }
.si-platformio::before { content: "\f21f"; }
.si-platformio.si--color::before { color: #F5822A; }
.si-platzi::before { content: "\f220"; }
.si-platzi.si--color::before { color: #98CA3F; }
.si-plausibleanalytics::before { content: "\f221"; }
.si-plausibleanalytics.si--color::before { color: #5850EC; }
.si-playcanvas::before { content: "\f222"; }
.si-playcanvas.si--color::before { color: #E05F2C; }
.si-playerdotme::before { content: "\f223"; }
.si-playerdotme.si--color::before { color: #C0379A; }
.si-playerfm::before { content: "\f224"; }
.si-playerfm.si--color::before { color: #C8122A; }
.si-playstation::before { content: "\f225"; }
.si-playstation.si--color::before { color: #003791; }
.si-playstation2::before { content: "\f226"; }
.si-playstation2.si--color::before { color: #003791; }
.si-playstation3::before { content: "\f227"; }
.si-playstation3.si--color::before { color: #003791; }
.si-playstation4::before { content: "\f228"; }
.si-playstation4.si--color::before { color: #003791; }
.si-playstation5::before { content: "\f229"; }
.si-playstation5.si--color::before { color: #003791; }
.si-playstationportable::before { content: "\f22a"; }
.si-playstationportable.si--color::before { color: #003791; }
.si-playstationvita::before { content: "\f22b"; }
.si-playstationvita.si--color::before { color: #003791; }
.si-pleroma::before { content: "\f22c"; }
.si-pleroma.si--color::before { color: #FBA457; }
.si-plesk::before { content: "\f22d"; }
.si-plesk.si--color::before { color: #52BBE6; }
.si-plex::before { content: "\f22e"; }
.si-plex.si--color::before { color: #EBAF00; }
.si-plotly::before { content: "\f22f"; }
.si-plotly.si--color::before { color: #3F4F75; }
.si-plume::before { content: "\f230"; }
.si-plume.si--color::before { color: #7C5CDF; }
.si-pluralsight::before { content: "\f231"; }
.si-pluralsight.si--color::before { color: #F15B2A; }
.si-plurk::before { content: "\f232"; }
.si-plurk.si--color::before { color: #FF574D; }
.si-pluscodes::before { content: "\f233"; }
.si-pluscodes.si--color::before { color: #4285F4; }
.si-pm2::before { content: "\f234"; }
.si-pm2.si--color::before { color: #2B037A; }
.si-pnpm::before { content: "\f235"; }
.si-pnpm.si--color::before { color: #F69220; }
.si-pocket::before { content: "\f236"; }
.si-pocket.si--color::before { color: #EF3F56; }
.si-pocketbase::before { content: "\f237"; }
.si-pocketbase.si--color::before { color: #B8DBE4; }
.si-pocketcasts::before { content: "\f238"; }
.si-pocketcasts.si--color::before { color: #F43E37; }
.si-podcastaddict::before { content: "\f239"; }
.si-podcastaddict.si--color::before { color: #F4842D; }
.si-podcastindex::before { content: "\f23a"; }
.si-podcastindex.si--color::before { color: #F90000; }
.si-podman::before { content: "\f23b"; }
.si-podman.si--color::before { color: #892CA0; }
.si-poe::before { content: "\f23c"; }
.si-poe.si--color::before { color: #5D5CDE; }
.si-poetry::before { content: "\f23d"; }
.si-poetry.si--color::before { color: #60A5FA; }
.si-pointy::before { content: "\f23e"; }
.si-pointy.si--color::before { color: #009DE0; }
.si-pokemon::before { content: "\f23f"; }
.si-pokemon.si--color::before { color: #FFCB05; }
.si-polars::before { content: "\f240"; }
.si-polars.si--color::before { color: #CD792C; }
.si-polestar::before { content: "\f241"; }
.si-polestar.si--color::before { color: #000000; }
.si-polkadot::before { content: "\f242"; }
.si-polkadot.si--color::before { color: #E6007A; }
.si-poly::before { content: "\f243"; }
.si-poly.si--color::before { color: #EB3C00; }
.si-polygon::before { content: "\f244"; }
.si-polygon.si--color::before { color: #7B3FE4; }
.si-polymerproject::before { content: "\f245"; }
.si-polymerproject.si--color::before { color: #FF4470; }
.si-polywork::before { content: "\f246"; }
.si-polywork.si--color::before { color: #543DE0; }
.si-pond5::before { content: "\f247"; }
.si-pond5.si--color::before { color: #000000; }
.si-popos::before { content: "\f248"; }
.si-popos.si--color::before { color: #48B9C7; }
.si-porkbun::before { content: "\f249"; }
.si-porkbun.si--color::before { color: #EF7878; }
.si-porsche::before { content: "\f24a"; }
.si-porsche.si--color::before { color: #B12B28; }
.si-portainer::before { content: "\f24b"; }
.si-portainer.si--color::before { color: #13BEF9; }
.si-portswigger::before { content: "\f24c"; }
.si-portswigger.si--color::before { color: #FF6633; }
.si-posit::before { content: "\f24d"; }
.si-posit.si--color::before { color: #447099; }
.si-postcss::before { content: "\f24e"; }
.si-postcss.si--color::before { color: #DD3A0A; }
.si-postgresql::before { content: "\f24f"; }
.si-postgresql.si--color::before { color: #4169E1; }
.si-posthog::before { content: "\f250"; }
.si-posthog.si--color::before { color: #000000; }
.si-postman::before { content: "\f251"; }
.si-postman.si--color::before { color: #FF6C37; }
.si-postmates::before { content: "\f252"; }
.si-postmates.si--color::before { color: #FFDF18; }
.si-powers::before { content: "\f253"; }
.si-powers.si--color::before { color: #E74536; }
.si-prdotco::before { content: "\f254"; }
.si-prdotco.si--color::before { color: #0080FF; }
.si-preact::before { content: "\f255"; }
.si-preact.si--color::before { color: #673AB8; }
.si-precommit::before { content: "\f256"; }
.si-precommit.si--color::before { color: #FAB040; }
.si-prefect::before { content: "\f257"; }
.si-prefect.si--color::before { color: #070E10; }
.si-premierleague::before { content: "\f258"; }
.si-premierleague.si--color::before { color: #360D3A; }
.si-prepbytes::before { content: "\f259"; }
.si-prepbytes.si--color::before { color: #5A87C6; }
.si-prestashop::before { content: "\f25a"; }
.si-prestashop.si--color::before { color: #DF0067; }
.si-presto::before { content: "\f25b"; }
.si-presto.si--color::before { color: #5890FF; }
.si-prettier::before { content: "\f25c"; }
.si-prettier.si--color::before { color: #F7B93E; }
.si-pretzel::before { content: "\f25d"; }
.si-pretzel.si--color::before { color: #1BB3A4; }
.si-prevention::before { content: "\f25e"; }
.si-prevention.si--color::before { color: #44C1C5; }
.si-prezi::before { content: "\f25f"; }
.si-prezi.si--color::before { color: #3181FF; }
.si-prime::before { content: "\f260"; }
.si-prime.si--color::before { color: #00A8E1; }
.si-primeng::before { content: "\f261"; }
.si-primeng.si--color::before { color: #DD0031; }
.si-primereact::before { content: "\f262"; }
.si-primereact.si--color::before { color: #03C4E8; }
.si-primevideo::before { content: "\f263"; }
.si-primevideo.si--color::before { color: #1F2E3E; }
.si-printables::before { content: "\f264"; }
.si-printables.si--color::before { color: #FA6831; }
.si-prisma::before { content: "\f265"; }
.si-prisma.si--color::before { color: #2D3748; }
.si-prismic::before { content: "\f266"; }
.si-prismic.si--color::before { color: #5163BA; }
.si-privatedivision::before { content: "\f267"; }
.si-privatedivision.si--color::before { color: #000000; }
.si-privateinternetaccess::before { content: "\f268"; }
.si-privateinternetaccess.si--color::before { color: #1E811F; }
.si-probot::before { content: "\f269"; }
.si-probot.si--color::before { color: #00B0D8; }
.si-processingfoundation::before { content: "\f26a"; }
.si-processingfoundation.si--color::before { color: #006699; }
.si-processwire::before { content: "\f26b"; }
.si-processwire.si--color::before { color: #2480E6; }
.si-producthunt::before { content: "\f26c"; }
.si-producthunt.si--color::before { color: #DA552F; }
.si-progate::before { content: "\f26d"; }
.si-progate.si--color::before { color: #380953; }
.si-progress::before { content: "\f26e"; }
.si-progress.si--color::before { color: #5CE500; }
.si-prometheus::before { content: "\f26f"; }
.si-prometheus.si--color::before { color: #E6522C; }
.si-pronounsdotpage::before { content: "\f270"; }
.si-pronounsdotpage.si--color::before { color: #C71585; }
.si-prosieben::before { content: "\f271"; }
.si-prosieben.si--color::before { color: #E6000F; }
.si-proteus::before { content: "\f272"; }
.si-proteus.si--color::before { color: #1C79B3; }
.si-protocolsdotio::before { content: "\f273"; }
.si-protocolsdotio.si--color::before { color: #4D9FE7; }
.si-protodotio::before { content: "\f274"; }
.si-protodotio.si--color::before { color: #34A7C1; }
.si-proton::before { content: "\f275"; }
.si-proton.si--color::before { color: #6D4AFF; }
.si-protoncalendar::before { content: "\f276"; }
.si-protoncalendar.si--color::before { color: #50B0E9; }
.si-protondb::before { content: "\f277"; }
.si-protondb.si--color::before { color: #F50057; }
.si-protondrive::before { content: "\f278"; }
.si-protondrive.si--color::before { color: #EB508D; }
.si-protonmail::before { content: "\f279"; }
.si-protonmail.si--color::before { color: #6D4AFF; }
.si-protonvpn::before { content: "\f27a"; }
.si-protonvpn.si--color::before { color: #66DEB1; }
.si-protools::before { content: "\f27b"; }
.si-protools.si--color::before { color: #7ACB10; }
.si-protractor::before { content: "\f27c"; }
.si-protractor.si--color::before { color: #ED163A; }
.si-proxmox::before { content: "\f27d"; }
.si-proxmox.si--color::before { color: #E57000; }
.si-pterodactyl::before { content: "\f27e"; }
.si-pterodactyl.si--color::before { color: #10539F; }
.si-pubg::before { content: "\f27f"; }
.si-pubg.si--color::before { color: #FEAB02; }
.si-publons::before { content: "\f280"; }
.si-publons.si--color::before { color: #336699; }
.si-pubmed::before { content: "\f281"; }
.si-pubmed.si--color::before { color: #326599; }
.si-pug::before { content: "\f282"; }
.si-pug.si--color::before { color: #A86454; }
.si-pulumi::before { content: "\f283"; }
.si-pulumi.si--color::before { color: #8A3391; }
.si-puma::before { content: "\f284"; }
.si-puma.si--color::before { color: #242B2F; }
.si-puppet::before { content: "\f285"; }
.si-puppet.si--color::before { color: #FFAE1A; }
.si-puppeteer::before { content: "\f286"; }
.si-puppeteer.si--color::before { color: #40B5A4; }
.si-purescript::before { content: "\f287"; }
.si-purescript.si--color::before { color: #14161A; }
.si-purgecss::before { content: "\f288"; }
.si-purgecss.si--color::before { color: #14161A; }
.si-purism::before { content: "\f289"; }
.si-purism.si--color::before { color: #2D2D2D; }
.si-pushbullet::before { content: "\f28a"; }
.si-pushbullet.si--color::before { color: #4AB367; }
.si-pusher::before { content: "\f28b"; }
.si-pusher.si--color::before { color: #300D4F; }
.si-pwa::before { content: "\f28c"; }
.si-pwa.si--color::before { color: #5A0FC8; }
.si-pycharm::before { content: "\f28d"; }
.si-pycharm.si--color::before { color: #000000; }
.si-pycqa::before { content: "\f28e"; }
.si-pycqa.si--color::before { color: #201B44; }
.si-pydantic::before { content: "\f28f"; }
.si-pydantic.si--color::before { color: #E92063; }
.si-pyg::before { content: "\f290"; }
.si-pyg.si--color::before { color: #3C2179; }
.si-pypi::before { content: "\f291"; }
.si-pypi.si--color::before { color: #3775A9; }
.si-pypy::before { content: "\f292"; }
.si-pypy.si--color::before { color: #193440; }
.si-pyscaffold::before { content: "\f293"; }
.si-pyscaffold.si--color::before { color: #005CA0; }
.si-pysyft::before { content: "\f294"; }
.si-pysyft.si--color::before { color: #F1BF7A; }
.si-pytest::before { content: "\f295"; }
.si-pytest.si--color::before { color: #0A9EDC; }
.si-python::before { content: "\f296"; }
.si-python.si--color::before { color: #3776AB; }
.si-pythonanywhere::before { content: "\f297"; }
.si-pythonanywhere.si--color::before { color: #1D9FD7; }
.si-pytorch::before { content: "\f298"; }
.si-pytorch.si--color::before { color: #EE4C2C; }
.si-pyup::before { content: "\f299"; }
.si-pyup.si--color::before { color: #9F55FF; }
.si-qantas::before { content: "\f29a"; }
.si-qantas.si--color::before { color: #E40000; }
.si-qase::before { content: "\f29b"; }
.si-qase.si--color::before { color: #4F46DC; }
.si-qatarairways::before { content: "\f29c"; }
.si-qatarairways.si--color::before { color: #5C0D34; }
.si-qbittorrent::before { content: "\f29d"; }
.si-qbittorrent.si--color::before { color: #2F67BA; }
.si-qemu::before { content: "\f29e"; }
.si-qemu.si--color::before { color: #FF6600; }
.si-qgis::before { content: "\f29f"; }
.si-qgis.si--color::before { color: #589632; }
.si-qi::before { content: "\f2a0"; }
.si-qi.si--color::before { color: #000000; }
.si-qiita::before { content: "\f2a1"; }
.si-qiita.si--color::before { color: #55C500; }
.si-qiskit::before { content: "\f2a2"; }
.si-qiskit.si--color::before { color: #6929C4; }
.si-qiwi::before { content: "\f2a3"; }
.si-qiwi.si--color::before { color: #FF8C00; }
.si-qlik::before { content: "\f2a4"; }
.si-qlik.si--color::before { color: #009848; }
.si-qmk::before { content: "\f2a5"; }
.si-qmk.si--color::before { color: #333333; }
.si-qnap::before { content: "\f2a6"; }
.si-qnap.si--color::before { color: #0C2E82; }
.si-qt::before { content: "\f2a7"; }
.si-qt.si--color::before { color: #41CD52; }
.si-qualcomm::before { content: "\f2a8"; }
.si-qualcomm.si--color::before { color: #3253DC; }
.si-qualtrics::before { content: "\f2a9"; }
.si-qualtrics.si--color::before { color: #00B4EF; }
.si-qualys::before { content: "\f2aa"; }
.si-qualys.si--color::before { color: #ED2E26; }
.si-quantcast::before { content: "\f2ab"; }
.si-quantcast.si--color::before { color: #000000; }
.si-quantconnect::before { content: "\f2ac"; }
.si-quantconnect.si--color::before { color: #F98309; }
.si-quarkus::before { content: "\f2ad"; }
.si-quarkus.si--color::before { color: #4695EB; }
.si-quarto::before { content: "\f2ae"; }
.si-quarto.si--color::before { color: #39729E; }
.si-quasar::before { content: "\f2af"; }
.si-quasar.si--color::before { color: #050A14; }
.si-qubesos::before { content: "\f2b0"; }
.si-qubesos.si--color::before { color: #3874D8; }
.si-quest::before { content: "\f2b1"; }
.si-quest.si--color::before { color: #FB4F14; }
.si-quickbooks::before { content: "\f2b2"; }
.si-quickbooks.si--color::before { color: #2CA01C; }
.si-quicklook::before { content: "\f2b3"; }
.si-quicklook.si--color::before { color: #0078D3; }
.si-quicktime::before { content: "\f2b4"; }
.si-quicktime.si--color::before { color: #1C69F0; }
.si-quicktype::before { content: "\f2b5"; }
.si-quicktype.si--color::before { color: #159588; }
.si-quip::before { content: "\f2b6"; }
.si-quip.si--color::before { color: #F27557; }
.si-quizlet::before { content: "\f2b7"; }
.si-quizlet.si--color::before { color: #4255FF; }
.si-quora::before { content: "\f2b8"; }
.si-quora.si--color::before { color: #B92B27; }
.si-qwant::before { content: "\f2b9"; }
.si-qwant.si--color::before { color: #5C97FF; }
.si-qwik::before { content: "\f2ba"; }
.si-qwik.si--color::before { color: #AC7EF4; }
.si-qwiklabs::before { content: "\f2bb"; }
.si-qwiklabs.si--color::before { color: #F5CD0E; }
.si-qzone::before { content: "\f2bc"; }
.si-qzone.si--color::before { color: #FECE00; }
.si-r::before { content: "\f2bd"; }
.si-r.si--color::before { color: #276DC3; }
.si-r3::before { content: "\f2be"; }
.si-r3.si--color::before { color: #EC1D24; }
.si-rabbitmq::before { content: "\f2bf"; }
.si-rabbitmq.si--color::before { color: #FF6600; }
.si-racket::before { content: "\f2c0"; }
.si-racket.si--color::before { color: #9F1D20; }
.si-radar::before { content: "\f2c1"; }
.si-radar.si--color::before { color: #007AFF; }
.si-radarr::before { content: "\f2c2"; }
.si-radarr.si--color::before { color: #FFCB3D; }
.si-radiopublic::before { content: "\f2c3"; }
.si-radiopublic.si--color::before { color: #CE262F; }
.si-radixui::before { content: "\f2c4"; }
.si-radixui.si--color::before { color: #161618; }
.si-radstudio::before { content: "\f2c5"; }
.si-radstudio.si--color::before { color: #E62431; }
.si-railway::before { content: "\f2c6"; }
.si-railway.si--color::before { color: #0B0D0E; }
.si-rainmeter::before { content: "\f2c7"; }
.si-rainmeter.si--color::before { color: #19519B; }
.si-rakuten::before { content: "\f2c8"; }
.si-rakuten.si--color::before { color: #BF0000; }
.si-ram::before { content: "\f2c9"; }
.si-ram.si--color::before { color: #000000; }
.si-rancher::before { content: "\f2ca"; }
.si-rancher.si--color::before { color: #0075A8; }
.si-rapid::before { content: "\f2cb"; }
.si-rapid.si--color::before { color: #0055DA; }
.si-rarible::before { content: "\f2cc"; }
.si-rarible.si--color::before { color: #FEDA03; }
.si-rasa::before { content: "\f2cd"; }
.si-rasa.si--color::before { color: #5A17EE; }
.si-raspberrypi::before { content: "\f2ce"; }
.si-raspberrypi.si--color::before { color: #A22846; }
.si-ravelry::before { content: "\f2cf"; }
.si-ravelry.si--color::before { color: #EE6E62; }
.si-ray::before { content: "\f2d0"; }
.si-ray.si--color::before { color: #028CF0; }
.si-raycast::before { content: "\f2d1"; }
.si-raycast.si--color::before { color: #FF6363; }
.si-raylib::before { content: "\f2d2"; }
.si-raylib.si--color::before { color: #000000; }
.si-razer::before { content: "\f2d3"; }
.si-razer.si--color::before { color: #00FF00; }
.si-razorpay::before { content: "\f2d4"; }
.si-razorpay.si--color::before { color: #0C2451; }
.si-rclone::before { content: "\f2d5"; }
.si-rclone.si--color::before { color: #3F79AD; }
.si-react::before { content: "\f2d6"; }
.si-react.si--color::before { color: #61DAFB; }
.si-reactbootstrap::before { content: "\f2d7"; }
.si-reactbootstrap.si--color::before { color: #41E0FD; }
.si-reacthookform::before { content: "\f2d8"; }
.si-reacthookform.si--color::before { color: #EC5990; }
.si-reactiveresume::before { content: "\f2d9"; }
.si-reactiveresume.si--color::before { color: #000000; }
.si-reactivex::before { content: "\f2da"; }
.si-reactivex.si--color::before { color: #B7178C; }
.si-reactos::before { content: "\f2db"; }
.si-reactos.si--color::before { color: #0088CC; }
.si-reactquery::before { content: "\f2dc"; }
.si-reactquery.si--color::before { color: #FF4154; }
.si-reactrouter::before { content: "\f2dd"; }
.si-reactrouter.si--color::before { color: #CA4245; }
.si-reacttable::before { content: "\f2de"; }
.si-reacttable.si--color::before { color: #FF4154; }
.si-readdotcv::before { content: "\f2df"; }
.si-readdotcv.si--color::before { color: #111111; }
.si-readme::before { content: "\f2e0"; }
.si-readme.si--color::before { color: #018EF5; }
.si-readthedocs::before { content: "\f2e1"; }
.si-readthedocs.si--color::before { color: #8CA1AF; }
.si-realm::before { content: "\f2e2"; }
.si-realm.si--color::before { color: #39477F; }
.si-reason::before { content: "\f2e3"; }
.si-reason.si--color::before { color: #DD4B39; }
.si-reasonstudios::before { content: "\f2e4"; }
.si-reasonstudios.si--color::before { color: #FFFFFF; }
.si-recoil::before { content: "\f2e5"; }
.si-recoil.si--color::before { color: #3578E5; }
.si-red::before { content: "\f2e6"; }
.si-red.si--color::before { color: #B32629; }
.si-redash::before { content: "\f2e7"; }
.si-redash.si--color::before { color: #FF7964; }
.si-redbubble::before { content: "\f2e8"; }
.si-redbubble.si--color::before { color: #E41321; }
.si-redbull::before { content: "\f2e9"; }
.si-redbull.si--color::before { color: #DB0A40; }
.si-reddit::before { content: "\f2ea"; }
.si-reddit.si--color::before { color: #FF4500; }
.si-redhat::before { content: "\f2eb"; }
.si-redhat.si--color::before { color: #EE0000; }
.si-redhatopenshift::before { content: "\f2ec"; }
.si-redhatopenshift.si--color::before { color: #EE0000; }
.si-redis::before { content: "\f2ed"; }
.si-redis.si--color::before { color: #FF4438; }
.si-redmine::before { content: "\f2ee"; }
.si-redmine.si--color::before { color: #B32024; }
.si-redox::before { content: "\f2ef"; }
.si-redox.si--color::before { color: #000000; }
.si-redsys::before { content: "\f2f0"; }
.si-redsys.si--color::before { color: #DC7C26; }
.si-redux::before { content: "\f2f1"; }
.si-redux.si--color::before { color: #764ABC; }
.si-reduxsaga::before { content: "\f2f2"; }
.si-reduxsaga.si--color::before { color: #999999; }
.si-redwoodjs::before { content: "\f2f3"; }
.si-redwoodjs.si--color::before { color: #BF4722; }
.si-reebok::before { content: "\f2f4"; }
.si-reebok.si--color::before { color: #E41D1B; }
.si-refine::before { content: "\f2f5"; }
.si-refine.si--color::before { color: #14141F; }
.si-relay::before { content: "\f2f6"; }
.si-relay.si--color::before { color: #F26B00; }
.si-relianceindustrieslimited::before { content: "\f2f7"; }
.si-relianceindustrieslimited.si--color::before { color: #D1AB66; }
.si-remark::before { content: "\f2f8"; }
.si-remark.si--color::before { color: #000000; }
.si-remedyentertainment::before { content: "\f2f9"; }
.si-remedyentertainment.si--color::before { color: #D6001C; }
.si-remix::before { content: "\f2fa"; }
.si-remix.si--color::before { color: #000000; }
.si-removedotbg::before { content: "\f2fb"; }
.si-removedotbg.si--color::before { color: #54616C; }
.si-renault::before { content: "\f2fc"; }
.si-renault.si--color::before { color: #FFCC33; }
.si-render::before { content: "\f2fd"; }
.si-render.si--color::before { color: #000000; }
.si-renovate::before { content: "\f2fe"; }
.si-renovate.si--color::before { color: #1A1F6C; }
.si-renpy::before { content: "\f2ff"; }
.si-renpy.si--color::before { color: #FF7F7F; }
.si-renren::before { content: "\f300"; }
.si-renren.si--color::before { color: #217DC6; }
.si-replit::before { content: "\f301"; }
.si-replit.si--color::before { color: #F26207; }
.si-republicofgamers::before { content: "\f302"; }
.si-republicofgamers.si--color::before { color: #FF0029; }
.si-rescript::before { content: "\f303"; }
.si-rescript.si--color::before { color: #E6484F; }
.si-rescuetime::before { content: "\f304"; }
.si-rescuetime.si--color::before { color: #161A3B; }
.si-researchgate::before { content: "\f305"; }
.si-researchgate.si--color::before { color: #00CCBB; }
.si-resend::before { content: "\f306"; }
.si-resend.si--color::before { color: #000000; }
.si-resharper::before { content: "\f307"; }
.si-resharper.si--color::before { color: #000000; }
.si-resurrectionremixos::before { content: "\f308"; }
.si-resurrectionremixos.si--color::before { color: #000000; }
.si-retool::before { content: "\f309"; }
.si-retool.si--color::before { color: #3D3D3D; }
.si-retroarch::before { content: "\f30a"; }
.si-retroarch.si--color::before { color: #000000; }
.si-retropie::before { content: "\f30b"; }
.si-retropie.si--color::before { color: #CC0000; }
.si-revanced::before { content: "\f30c"; }
.si-revanced.si--color::before { color: #9ED5FF; }
.si-revealdotjs::before { content: "\f30d"; }
.si-revealdotjs.si--color::before { color: #F2E142; }
.si-reverbnation::before { content: "\f30e"; }
.si-reverbnation.si--color::before { color: #E43526; }
.si-revoltdotchat::before { content: "\f30f"; }
.si-revoltdotchat.si--color::before { color: #FF4655; }
.si-revolut::before { content: "\f310"; }
.si-revolut.si--color::before { color: #191C1F; }
.si-revue::before { content: "\f311"; }
.si-revue.si--color::before { color: #E15718; }
.si-rewe::before { content: "\f312"; }
.si-rewe.si--color::before { color: #CC071E; }
.si-rezgo::before { content: "\f313"; }
.si-rezgo.si--color::before { color: #F76C00; }
.si-rhinoceros::before { content: "\f314"; }
.si-rhinoceros.si--color::before { color: #801010; }
.si-rich::before { content: "\f315"; }
.si-rich.si--color::before { color: #FAE742; }
.si-rider::before { content: "\f316"; }
.si-rider.si--color::before { color: #000000; }
.si-rimacautomobili::before { content: "\f317"; }
.si-rimacautomobili.si--color::before { color: #0A222E; }
.si-rime::before { content: "\f318"; }
.si-rime.si--color::before { color: #000000; }
.si-ring::before { content: "\f319"; }
.si-ring.si--color::before { color: #1C9AD6; }
.si-riotgames::before { content: "\f31a"; }
.si-riotgames.si--color::before { color: #EB0029; }
.si-ripple::before { content: "\f31b"; }
.si-ripple.si--color::before { color: #0085C0; }
.si-riscv::before { content: "\f31c"; }
.si-riscv.si--color::before { color: #283272; }
.si-riseup::before { content: "\f31d"; }
.si-riseup.si--color::before { color: #FF0000; }
.si-ritzcarlton::before { content: "\f31e"; }
.si-ritzcarlton.si--color::before { color: #000000; }
.si-rive::before { content: "\f31f"; }
.si-rive.si--color::before { color: #1D1D1D; }
.si-roadmapdotsh::before { content: "\f320"; }
.si-roadmapdotsh.si--color::before { color: #000000; }
.si-roamresearch::before { content: "\f321"; }
.si-roamresearch.si--color::before { color: #343A40; }
.si-robinhood::before { content: "\f322"; }
.si-robinhood.si--color::before { color: #00C805; }
.si-roblox::before { content: "\f323"; }
.si-roblox.si--color::before { color: #000000; }
.si-robloxstudio::before { content: "\f324"; }
.si-robloxstudio.si--color::before { color: #00A2FF; }
.si-robotframework::before { content: "\f325"; }
.si-robotframework.si--color::before { color: #000000; }
.si-rocket::before { content: "\f326"; }
.si-rocket.si--color::before { color: #D33847; }
.si-rocketdotchat::before { content: "\f327"; }
.si-rocketdotchat.si--color::before { color: #F5455C; }
.si-rocksdb::before { content: "\f328"; }
.si-rocksdb.si--color::before { color: #2A2A2A; }
.si-rockstargames::before { content: "\f329"; }
.si-rockstargames.si--color::before { color: #FCAF17; }
.si-rockwellautomation::before { content: "\f32a"; }
.si-rockwellautomation.si--color::before { color: #CD163F; }
.si-rockylinux::before { content: "\f32b"; }
.si-rockylinux.si--color::before { color: #10B981; }
.si-roku::before { content: "\f32c"; }
.si-roku.si--color::before { color: #662D91; }
.si-roll20::before { content: "\f32d"; }
.si-roll20.si--color::before { color: #E10085; }
.si-rollsroyce::before { content: "\f32e"; }
.si-rollsroyce.si--color::before { color: #281432; }
.si-rollupdotjs::before { content: "\f32f"; }
.si-rollupdotjs.si--color::before { color: #EC4A3F; }
.si-roon::before { content: "\f330"; }
.si-roon.si--color::before { color: #2039F3; }
.si-rootme::before { content: "\f331"; }
.si-rootme.si--color::before { color: #000000; }
.si-roots::before { content: "\f332"; }
.si-roots.si--color::before { color: #525DDC; }
.si-rootsbedrock::before { content: "\f333"; }
.si-rootsbedrock.si--color::before { color: #525DDC; }
.si-rootssage::before { content: "\f334"; }
.si-rootssage.si--color::before { color: #525DDC; }
.si-ros::before { content: "\f335"; }
.si-ros.si--color::before { color: #22314E; }
.si-rossmann::before { content: "\f336"; }
.si-rossmann.si--color::before { color: #C3002D; }
.si-rotaryinternational::before { content: "\f337"; }
.si-rotaryinternational.si--color::before { color: #F7A81B; }
.si-rottentomatoes::before { content: "\f338"; }
.si-rottentomatoes.si--color::before { color: #FA320A; }
.si-roundcube::before { content: "\f339"; }
.si-roundcube.si--color::before { color: #37BEFF; }
.si-rsocket::before { content: "\f33a"; }
.si-rsocket.si--color::before { color: #EF0092; }
.si-rss::before { content: "\f33b"; }
.si-rss.si--color::before { color: #FFA500; }
.si-rstudioide::before { content: "\f33c"; }
.si-rstudioide.si--color::before { color: #75AADB; }
.si-rte::before { content: "\f33d"; }
.si-rte.si--color::before { color: #00A7B3; }
.si-rtl::before { content: "\f33e"; }
.si-rtl.si--color::before { color: #FA002E; }
.si-rtlzwei::before { content: "\f33f"; }
.si-rtlzwei.si--color::before { color: #00BCF6; }
.si-rtm::before { content: "\f340"; }
.si-rtm.si--color::before { color: #36474F; }
.si-rubocop::before { content: "\f341"; }
.si-rubocop.si--color::before { color: #000000; }
.si-ruby::before { content: "\f342"; }
.si-ruby.si--color::before { color: #CC342D; }
.si-rubygems::before { content: "\f343"; }
.si-rubygems.si--color::before { color: #E9573F; }
.si-rubymine::before { content: "\f344"; }
.si-rubymine.si--color::before { color: #000000; }
.si-rubyonrails::before { content: "\f345"; }
.si-rubyonrails.si--color::before { color: #D30001; }
.si-rubysinatra::before { content: "\f346"; }
.si-rubysinatra.si--color::before { color: #000000; }
.si-ruff::before { content: "\f347"; }
.si-ruff.si--color::before { color: #D7FF64; }
.si-rumahweb::before { content: "\f348"; }
.si-rumahweb.si--color::before { color: #2EB4E3; }
.si-rumble::before { content: "\f349"; }
.si-rumble.si--color::before { color: #85C742; }
.si-rundeck::before { content: "\f34a"; }
.si-rundeck.si--color::before { color: #F73F39; }
.si-runkeeper::before { content: "\f34b"; }
.si-runkeeper.si--color::before { color: #001E62; }
.si-runkit::before { content: "\f34c"; }
.si-runkit.si--color::before { color: #491757; }
.si-runrundotit::before { content: "\f34d"; }
.si-runrundotit.si--color::before { color: #DB3729; }
.si-rust::before { content: "\f34e"; }
.si-rust.si--color::before { color: #000000; }
.si-rustdesk::before { content: "\f34f"; }
.si-rustdesk.si--color::before { color: #024EFF; }
.si-rxdb::before { content: "\f350"; }
.si-rxdb.si--color::before { color: #8D1F89; }
.si-ryanair::before { content: "\f351"; }
.si-ryanair.si--color::before { color: #073590; }
.si-rye::before { content: "\f352"; }
.si-rye.si--color::before { color: #000000; }
.si-s7airlines::before { content: "\f353"; }
.si-s7airlines.si--color::before { color: #C4D600; }
.si-sabanci::before { content: "\f354"; }
.si-sabanci.si--color::before { color: #004B93; }
.si-safari::before { content: "\f355"; }
.si-safari.si--color::before { color: #006CFF; }
.si-sage::before { content: "\f356"; }
.si-sage.si--color::before { color: #00D639; }
.si-sahibinden::before { content: "\f357"; }
.si-sahibinden.si--color::before { color: #FFE800; }
.si-sailfishos::before { content: "\f358"; }
.si-sailfishos.si--color::before { color: #053766; }
.si-sailsdotjs::before { content: "\f359"; }
.si-sailsdotjs.si--color::before { color: #14ACC2; }
.si-salesforce::before { content: "\f35a"; }
.si-salesforce.si--color::before { color: #00A1E0; }
.si-salla::before { content: "\f35b"; }
.si-salla.si--color::before { color: #BAF3E6; }
.si-saltproject::before { content: "\f35c"; }
.si-saltproject.si--color::before { color: #57BCAD; }
.si-samsclub::before { content: "\f35d"; }
.si-samsclub.si--color::before { color: #0067A0; }
.si-samsung::before { content: "\f35e"; }
.si-samsung.si--color::before { color: #1428A0; }
.si-samsungpay::before { content: "\f35f"; }
.si-samsungpay.si--color::before { color: #1428A0; }
.si-sandisk::before { content: "\f360"; }
.si-sandisk.si--color::before { color: #ED1C24; }
.si-sanfranciscomunicipalrailway::before { content: "\f361"; }
.si-sanfranciscomunicipalrailway.si--color::before { color: #BA0C2F; }
.si-sanic::before { content: "\f362"; }
.si-sanic.si--color::before { color: #FF0D68; }
.si-sanity::before { content: "\f363"; }
.si-sanity.si--color::before { color: #F03E2F; }
.si-saopaulometro::before { content: "\f364"; }
.si-saopaulometro.si--color::before { color: #004382; }
.si-sap::before { content: "\f365"; }
.si-sap.si--color::before { color: #0FAAFF; }
.si-sartorius::before { content: "\f366"; }
.si-sartorius.si--color::before { color: #FFED00; }
.si-sass::before { content: "\f367"; }
.si-sass.si--color::before { color: #CC6699; }
.si-sat1::before { content: "\f368"; }
.si-sat1.si--color::before { color: #047DA3; }
.si-satellite::before { content: "\f369"; }
.si-satellite.si--color::before { color: #000000; }
.si-saturn::before { content: "\f36a"; }
.si-saturn.si--color::before { color: #EB680B; }
.si-saucelabs::before { content: "\f36b"; }
.si-saucelabs.si--color::before { color: #3DDC91; }
.si-saudia::before { content: "\f36c"; }
.si-saudia.si--color::before { color: #026938; }
.si-scala::before { content: "\f36d"; }
.si-scala.si--color::before { color: #DC322F; }
.si-scaleway::before { content: "\f36e"; }
.si-scaleway.si--color::before { color: #4F0599; }
.si-scania::before { content: "\f36f"; }
.si-scania.si--color::before { color: #041E42; }
.si-schneiderelectric::before { content: "\f370"; }
.si-schneiderelectric.si--color::before { color: #3DCD58; }
.si-scikitlearn::before { content: "\f371"; }
.si-scikitlearn.si--color::before { color: #F7931E; }
.si-scilab::before { content: "\f372"; }
.si-scilab.si--color::before { color: #CD1925; }
.si-scipy::before { content: "\f373"; }
.si-scipy.si--color::before { color: #8CAAE6; }
.si-scopus::before { content: "\f374"; }
.si-scopus.si--color::before { color: #E9711C; }
.si-scpfoundation::before { content: "\f375"; }
.si-scpfoundation.si--color::before { color: #FFFFFF; }
.si-scrapbox::before { content: "\f376"; }
.si-scrapbox.si--color::before { color: #06B632; }
.si-scrapy::before { content: "\f377"; }
.si-scrapy.si--color::before { color: #60A839; }
.si-scratch::before { content: "\f378"; }
.si-scratch.si--color::before { color: #4D97FF; }
.si-screencastify::before { content: "\f379"; }
.si-screencastify.si--color::before { color: #FF8282; }
.si-scribd::before { content: "\f37a"; }
.si-scribd.si--color::before { color: #1E7B85; }
.si-scrimba::before { content: "\f37b"; }
.si-scrimba.si--color::before { color: #2B283A; }
.si-scrollreveal::before { content: "\f37c"; }
.si-scrollreveal.si--color::before { color: #FFCB36; }
.si-scrumalliance::before { content: "\f37d"; }
.si-scrumalliance.si--color::before { color: #009FDA; }
.si-scrutinizerci::before { content: "\f37e"; }
.si-scrutinizerci.si--color::before { color: #8A9296; }
.si-scylladb::before { content: "\f37f"; }
.si-scylladb.si--color::before { color: #6CD5E7; }
.si-seagate::before { content: "\f380"; }
.si-seagate.si--color::before { color: #6EBE49; }
.si-searxng::before { content: "\f381"; }
.si-searxng.si--color::before { color: #3050FF; }
.si-seat::before { content: "\f382"; }
.si-seat.si--color::before { color: #33302E; }
.si-seatgeek::before { content: "\f383"; }
.si-seatgeek.si--color::before { color: #FF5B49; }
.si-securityscorecard::before { content: "\f384"; }
.si-securityscorecard.si--color::before { color: #7033FD; }
.si-sefaria::before { content: "\f385"; }
.si-sefaria.si--color::before { color: #212E50; }
.si-sega::before { content: "\f386"; }
.si-sega.si--color::before { color: #0089CF; }
.si-selenium::before { content: "\f387"; }
.si-selenium.si--color::before { color: #43B02A; }
.si-sellfy::before { content: "\f388"; }
.si-sellfy.si--color::before { color: #21B352; }
.si-semanticrelease::before { content: "\f389"; }
.si-semanticrelease.si--color::before { color: #494949; }
.si-semanticscholar::before { content: "\f38a"; }
.si-semanticscholar.si--color::before { color: #1857B6; }
.si-semanticui::before { content: "\f38b"; }
.si-semanticui.si--color::before { color: #00B5AD; }
.si-semanticuireact::before { content: "\f38c"; }
.si-semanticuireact.si--color::before { color: #35BDB2; }
.si-semanticweb::before { content: "\f38d"; }
.si-semanticweb.si--color::before { color: #005A9C; }
.si-semaphoreci::before { content: "\f38e"; }
.si-semaphoreci.si--color::before { color: #19A974; }
.si-semrush::before { content: "\f38f"; }
.si-semrush.si--color::before { color: #FF642D; }
.si-semver::before { content: "\f390"; }
.si-semver.si--color::before { color: #3F4551; }
.si-sencha::before { content: "\f391"; }
.si-sencha.si--color::before { color: #86BC40; }
.si-sennheiser::before { content: "\f392"; }
.si-sennheiser.si--color::before { color: #000000; }
.si-sensu::before { content: "\f393"; }
.si-sensu.si--color::before { color: #89C967; }
.si-sentry::before { content: "\f394"; }
.si-sentry.si--color::before { color: #362D59; }
.si-sepa::before { content: "\f395"; }
.si-sepa.si--color::before { color: #2350A9; }
.si-sequelize::before { content: "\f396"; }
.si-sequelize.si--color::before { color: #52B0E7; }
.si-serverfault::before { content: "\f397"; }
.si-serverfault.si--color::before { color: #E7282D; }
.si-serverless::before { content: "\f398"; }
.si-serverless.si--color::before { color: #FD5750; }
.si-session::before { content: "\f399"; }
.si-session.si--color::before { color: #000000; }
.si-sessionize::before { content: "\f39a"; }
.si-sessionize.si--color::before { color: #1AB394; }
.si-setapp::before { content: "\f39b"; }
.si-setapp.si--color::before { color: #E6C3A5; }
.si-sfml::before { content: "\f39c"; }
.si-sfml.si--color::before { color: #8CC445; }
.si-shadcnui::before { content: "\f39d"; }
.si-shadcnui.si--color::before { color: #000000; }
.si-shadow::before { content: "\f39e"; }
.si-shadow.si--color::before { color: #0A0C0D; }
.si-shanghaimetro::before { content: "\f39f"; }
.si-shanghaimetro.si--color::before { color: #EC1C24; }
.si-sharex::before { content: "\f3a0"; }
.si-sharex.si--color::before { color: #2885F1; }
.si-sharp::before { content: "\f3a1"; }
.si-sharp.si--color::before { color: #99CC00; }
.si-shazam::before { content: "\f3a2"; }
.si-shazam.si--color::before { color: #0088FF; }
.si-shell::before { content: "\f3a3"; }
.si-shell.si--color::before { color: #FFD500; }
.si-shelly::before { content: "\f3a4"; }
.si-shelly.si--color::before { color: #4495D1; }
.si-shenzhenmetro::before { content: "\f3a5"; }
.si-shenzhenmetro.si--color::before { color: #009943; }
.si-shieldsdotio::before { content: "\f3a6"; }
.si-shieldsdotio.si--color::before { color: #000000; }
.si-shikimori::before { content: "\f3a7"; }
.si-shikimori.si--color::before { color: #343434; }
.si-shopee::before { content: "\f3a8"; }
.si-shopee.si--color::before { color: #EE4D2D; }
.si-shopify::before { content: "\f3a9"; }
.si-shopify.si--color::before { color: #7AB55C; }
.si-shopware::before { content: "\f3aa"; }
.si-shopware.si--color::before { color: #189EFF; }
.si-shortcut::before { content: "\f3ab"; }
.si-shortcut.si--color::before { color: #58B1E4; }
.si-showpad::before { content: "\f3ac"; }
.si-showpad.si--color::before { color: #2D2E83; }
.si-showtime::before { content: "\f3ad"; }
.si-showtime.si--color::before { color: #B10000; }
.si-showwcase::before { content: "\f3ae"; }
.si-showwcase.si--color::before { color: #0A0D14; }
.si-shutterstock::before { content: "\f3af"; }
.si-shutterstock.si--color::before { color: #EE2B24; }
.si-sidekiq::before { content: "\f3b0"; }
.si-sidekiq.si--color::before { color: #B1003E; }
.si-sidequest::before { content: "\f3b1"; }
.si-sidequest.si--color::before { color: #101227; }
.si-siemens::before { content: "\f3b2"; }
.si-siemens.si--color::before { color: #009999; }
.si-sifive::before { content: "\f3b3"; }
.si-sifive.si--color::before { color: #252323; }
.si-signal::before { content: "\f3b4"; }
.si-signal.si--color::before { color: #3A76F0; }
.si-similarweb::before { content: "\f3b5"; }
.si-similarweb.si--color::before { color: #092540; }
.si-simkl::before { content: "\f3b6"; }
.si-simkl.si--color::before { color: #000000; }
.si-simpleanalytics::before { content: "\f3b7"; }
.si-simpleanalytics.si--color::before { color: #FF4F64; }
.si-simpleicons::before { content: "\f3b8"; }
.si-simpleicons.si--color::before { color: #111111; }
.si-simplelogin::before { content: "\f3b9"; }
.si-simplelogin.si--color::before { color: #EA319F; }
.si-simplenote::before { content: "\f3ba"; }
.si-simplenote.si--color::before { color: #3361CC; }
.si-sinaweibo::before { content: "\f3bb"; }
.si-sinaweibo.si--color::before { color: #E6162D; }
.si-singaporeairlines::before { content: "\f3bc"; }
.si-singaporeairlines.si--color::before { color: #F99F1C; }
.si-singlestore::before { content: "\f3bd"; }
.si-singlestore.si--color::before { color: #AA00FF; }
.si-sitecore::before { content: "\f3be"; }
.si-sitecore.si--color::before { color: #EB1F1F; }
.si-sitepoint::before { content: "\f3bf"; }
.si-sitepoint.si--color::before { color: #258AAF; }
.si-siyuan::before { content: "\f3c0"; }
.si-siyuan.si--color::before { color: #D23F31; }
.si-skaffold::before { content: "\f3c1"; }
.si-skaffold.si--color::before { color: #2AA2D6; }
.si-sketch::before { content: "\f3c2"; }
.si-sketch.si--color::before { color: #F7B500; }
.si-sketchfab::before { content: "\f3c3"; }
.si-sketchfab.si--color::before { color: #1CAAD9; }
.si-sketchup::before { content: "\f3c4"; }
.si-sketchup.si--color::before { color: #005F9E; }
.si-skillshare::before { content: "\f3c5"; }
.si-skillshare.si--color::before { color: #00FF84; }
.si-skoda::before { content: "\f3c6"; }
.si-skoda.si--color::before { color: #0E3A2F; }
.si-sky::before { content: "\f3c7"; }
.si-sky.si--color::before { color: #0072C9; }
.si-skypack::before { content: "\f3c8"; }
.si-skypack.si--color::before { color: #3167FF; }
.si-skyrock::before { content: "\f3c9"; }
.si-skyrock.si--color::before { color: #009AFF; }
.si-slack::before { content: "\f3ca"; }
.si-slack.si--color::before { color: #4A154B; }
.si-slackware::before { content: "\f3cb"; }
.si-slackware.si--color::before { color: #000000; }
.si-slashdot::before { content: "\f3cc"; }
.si-slashdot.si--color::before { color: #026664; }
.si-slickpic::before { content: "\f3cd"; }
.si-slickpic.si--color::before { color: #FF880F; }
.si-slides::before { content: "\f3ce"; }
.si-slides.si--color::before { color: #E4637C; }
.si-slideshare::before { content: "\f3cf"; }
.si-slideshare.si--color::before { color: #008ED2; }
.si-slint::before { content: "\f3d0"; }
.si-slint.si--color::before { color: #2379F4; }
.si-smart::before { content: "\f3d1"; }
.si-smart.si--color::before { color: #D7E600; }
.si-smartthings::before { content: "\f3d2"; }
.si-smartthings.si--color::before { color: #15BFFF; }
.si-smashdotgg::before { content: "\f3d3"; }
.si-smashdotgg.si--color::before { color: #CB333B; }
.si-smashingmagazine::before { content: "\f3d4"; }
.si-smashingmagazine.si--color::before { color: #E85C33; }
.si-smrt::before { content: "\f3d5"; }
.si-smrt.si--color::before { color: #EE2E24; }
.si-smugmug::before { content: "\f3d6"; }
.si-smugmug.si--color::before { color: #6DB944; }
.si-snapchat::before { content: "\f3d7"; }
.si-snapchat.si--color::before { color: #FFFC00; }
.si-snapcraft::before { content: "\f3d8"; }
.si-snapcraft.si--color::before { color: #82BEA0; }
.si-sncf::before { content: "\f3d9"; }
.si-sncf.si--color::before { color: #CA0939; }
.si-snort::before { content: "\f3da"; }
.si-snort.si--color::before { color: #F6A7AA; }
.si-snowflake::before { content: "\f3db"; }
.si-snowflake.si--color::before { color: #29B5E8; }
.si-snowpack::before { content: "\f3dc"; }
.si-snowpack.si--color::before { color: #2E5E82; }
.si-snyk::before { content: "\f3dd"; }
.si-snyk.si--color::before { color: #4C4A73; }
.si-socialblade::before { content: "\f3de"; }
.si-socialblade.si--color::before { color: #B3382C; }
.si-society6::before { content: "\f3df"; }
.si-society6.si--color::before { color: #000000; }
.si-socketdotio::before { content: "\f3e0"; }
.si-socketdotio.si--color::before { color: #010101; }
.si-softpedia::before { content: "\f3e1"; }
.si-softpedia.si--color::before { color: #002873; }
.si-sogou::before { content: "\f3e2"; }
.si-sogou.si--color::before { color: #FB6022; }
.si-solana::before { content: "\f3e3"; }
.si-solana.si--color::before { color: #9945FF; }
.si-solid::before { content: "\f3e4"; }
.si-solid.si--color::before { color: #2C4F7C; }
.si-solidity::before { content: "\f3e5"; }
.si-solidity.si--color::before { color: #363636; }
.si-sololearn::before { content: "\f3e6"; }
.si-sololearn.si--color::before { color: #149EF2; }
.si-solus::before { content: "\f3e7"; }
.si-solus.si--color::before { color: #5294E2; }
.si-sonar::before { content: "\f3e8"; }
.si-sonar.si--color::before { color: #FD3456; }
.si-sonarcloud::before { content: "\f3e9"; }
.si-sonarcloud.si--color::before { color: #F3702A; }
.si-sonarlint::before { content: "\f3ea"; }
.si-sonarlint.si--color::before { color: #CB2029; }
.si-sonarqube::before { content: "\f3eb"; }
.si-sonarqube.si--color::before { color: #4E9BCD; }
.si-sonarr::before { content: "\f3ec"; }
.si-sonarr.si--color::before { color: #2596BE; }
.si-sonatype::before { content: "\f3ed"; }
.si-sonatype.si--color::before { color: #1B1C30; }
.si-songkick::before { content: "\f3ee"; }
.si-songkick.si--color::before { color: #F80046; }
.si-songoda::before { content: "\f3ef"; }
.si-songoda.si--color::before { color: #FC494A; }
.si-sonicwall::before { content: "\f3f0"; }
.si-sonicwall.si--color::before { color: #FF791A; }
.si-sonos::before { content: "\f3f1"; }
.si-sonos.si--color::before { color: #000000; }
.si-sony::before { content: "\f3f2"; }
.si-sony.si--color::before { color: #FFFFFF; }
.si-soriana::before { content: "\f3f3"; }
.si-soriana.si--color::before { color: #D52B1E; }
.si-soundcharts::before { content: "\f3f4"; }
.si-soundcharts.si--color::before { color: #0C1528; }
.si-soundcloud::before { content: "\f3f5"; }
.si-soundcloud.si--color::before { color: #FF5500; }
.si-sourceengine::before { content: "\f3f6"; }
.si-sourceengine.si--color::before { color: #F79A10; }
.si-sourceforge::before { content: "\f3f7"; }
.si-sourceforge.si--color::before { color: #FF6600; }
.si-sourcehut::before { content: "\f3f8"; }
.si-sourcehut.si--color::before { color: #000000; }
.si-sourcetree::before { content: "\f3f9"; }
.si-sourcetree.si--color::before { color: #0052CC; }
.si-southwestairlines::before { content: "\f3fa"; }
.si-southwestairlines.si--color::before { color: #304CB2; }
.si-spacemacs::before { content: "\f3fb"; }
.si-spacemacs.si--color::before { color: #9266CC; }
.si-spaceship::before { content: "\f3fc"; }
.si-spaceship.si--color::before { color: #394EFF; }
.si-spacex::before { content: "\f3fd"; }
.si-spacex.si--color::before { color: #000000; }
.si-spacy::before { content: "\f3fe"; }
.si-spacy.si--color::before { color: #09A3D5; }
.si-sparkar::before { content: "\f3ff"; }
.si-sparkar.si--color::before { color: #FF5C83; }
.si-sparkasse::before { content: "\f400"; }
.si-sparkasse.si--color::before { color: #FF0000; }
.si-sparkfun::before { content: "\f401"; }
.si-sparkfun.si--color::before { color: #E53525; }
.si-sparkpost::before { content: "\f402"; }
.si-sparkpost.si--color::before { color: #FA6423; }
.si-spdx::before { content: "\f403"; }
.si-spdx.si--color::before { color: #4398CC; }
.si-speakerdeck::before { content: "\f404"; }
.si-speakerdeck.si--color::before { color: #009287; }
.si-spectrum::before { content: "\f405"; }
.si-spectrum.si--color::before { color: #7B16FF; }
.si-speedtest::before { content: "\f406"; }
.si-speedtest.si--color::before { color: #141526; }
.si-speedypage::before { content: "\f407"; }
.si-speedypage.si--color::before { color: #1C71F9; }
.si-sphinx::before { content: "\f408"; }
.si-sphinx.si--color::before { color: #000000; }
.si-spigotmc::before { content: "\f409"; }
.si-spigotmc.si--color::before { color: #ED8106; }
.si-spine::before { content: "\f40a"; }
.si-spine.si--color::before { color: #FF4000; }
.si-spinnaker::before { content: "\f40b"; }
.si-spinnaker.si--color::before { color: #139BB4; }
.si-spinrilla::before { content: "\f40c"; }
.si-spinrilla.si--color::before { color: #460856; }
.si-splunk::before { content: "\f40d"; }
.si-splunk.si--color::before { color: #000000; }
.si-spoj::before { content: "\f40e"; }
.si-spoj.si--color::before { color: #337AB7; }
.si-spond::before { content: "\f40f"; }
.si-spond.si--color::before { color: #EE4353; }
.si-spotify::before { content: "\f410"; }
.si-spotify.si--color::before { color: #1DB954; }
.si-spotlight::before { content: "\f411"; }
.si-spotlight.si--color::before { color: #352A71; }
.si-spreadshirt::before { content: "\f412"; }
.si-spreadshirt.si--color::before { color: #00B2A5; }
.si-spreaker::before { content: "\f413"; }
.si-spreaker.si--color::before { color: #F5C300; }
.si-spring::before { content: "\f414"; }
.si-spring.si--color::before { color: #6DB33F; }
.si-spring_creators::before { content: "\f415"; }
.si-spring_creators.si--color::before { color: #000000; }
.si-springboot::before { content: "\f416"; }
.si-springboot.si--color::before { color: #6DB33F; }
.si-springsecurity::before { content: "\f417"; }
.si-springsecurity.si--color::before { color: #6DB33F; }
.si-spyderide::before { content: "\f418"; }
.si-spyderide.si--color::before { color: #FF0000; }
.si-sqlalchemy::before { content: "\f419"; }
.si-sqlalchemy.si--color::before { color: #D71F00; }
.si-sqlite::before { content: "\f41a"; }
.si-sqlite.si--color::before { color: #003B57; }
.si-square::before { content: "\f41b"; }
.si-square.si--color::before { color: #3E4348; }
.si-squareenix::before { content: "\f41c"; }
.si-squareenix.si--color::before { color: #ED1C24; }
.si-squarespace::before { content: "\f41d"; }
.si-squarespace.si--color::before { color: #000000; }
.si-srgssr::before { content: "\f41e"; }
.si-srgssr.si--color::before { color: #AF001E; }
.si-ssrn::before { content: "\f41f"; }
.si-ssrn.si--color::before { color: #154881; }
.si-sst::before { content: "\f420"; }
.si-sst.si--color::before { color: #E27152; }
.si-stackbit::before { content: "\f421"; }
.si-stackbit.si--color::before { color: #207BEA; }
.si-stackblitz::before { content: "\f422"; }
.si-stackblitz.si--color::before { color: #1269D3; }
.si-stackedit::before { content: "\f423"; }
.si-stackedit.si--color::before { color: #606060; }
.si-stackexchange::before { content: "\f424"; }
.si-stackexchange.si--color::before { color: #1E5397; }
.si-stackhawk::before { content: "\f425"; }
.si-stackhawk.si--color::before { color: #00CBC6; }
.si-stackoverflow::before { content: "\f426"; }
.si-stackoverflow.si--color::before { color: #F58025; }
.si-stackpath::before { content: "\f427"; }
.si-stackpath.si--color::before { color: #000000; }
.si-stackshare::before { content: "\f428"; }
.si-stackshare.si--color::before { color: #0690FA; }
.si-stadia::before { content: "\f429"; }
.si-stadia.si--color::before { color: #CD2640; }
.si-staffbase::before { content: "\f42a"; }
.si-staffbase.si--color::before { color: #00A4FD; }
.si-stagetimer::before { content: "\f42b"; }
.si-stagetimer.si--color::before { color: #00A66C; }
.si-standardjs::before { content: "\f42c"; }
.si-standardjs.si--color::before { color: #F3DF49; }
.si-standardresume::before { content: "\f42d"; }
.si-standardresume.si--color::before { color: #2A3FFB; }
.si-starbucks::before { content: "\f42e"; }
.si-starbucks.si--color::before { color: #006241; }
.si-stardock::before { content: "\f42f"; }
.si-stardock.si--color::before { color: #004B8D; }
.si-starlingbank::before { content: "\f430"; }
.si-starlingbank.si--color::before { color: #6935D3; }
.si-starship::before { content: "\f431"; }
.si-starship.si--color::before { color: #DD0B78; }
.si-startrek::before { content: "\f432"; }
.si-startrek.si--color::before { color: #FFE200; }
.si-starz::before { content: "\f433"; }
.si-starz.si--color::before { color: #082125; }
.si-statamic::before { content: "\f434"; }
.si-statamic.si--color::before { color: #FF269E; }
.si-statista::before { content: "\f435"; }
.si-statista.si--color::before { color: #001327; }
.si-statuspage::before { content: "\f436"; }
.si-statuspage.si--color::before { color: #172B4D; }
.si-statuspal::before { content: "\f437"; }
.si-statuspal.si--color::before { color: #4934BF; }
.si-steam::before { content: "\f438"; }
.si-steam.si--color::before { color: #000000; }
.si-steamdb::before { content: "\f439"; }
.si-steamdb.si--color::before { color: #000000; }
.si-steamdeck::before { content: "\f43a"; }
.si-steamdeck.si--color::before { color: #1A9FFF; }
.si-steamworks::before { content: "\f43b"; }
.si-steamworks.si--color::before { color: #1E1E1E; }
.si-steelseries::before { content: "\f43c"; }
.si-steelseries.si--color::before { color: #FF5200; }
.si-steem::before { content: "\f43d"; }
.si-steem.si--color::before { color: #171FC9; }
.si-steemit::before { content: "\f43e"; }
.si-steemit.si--color::before { color: #06D6A9; }
.si-steinberg::before { content: "\f43f"; }
.si-steinberg.si--color::before { color: #C90827; }
.si-stellar::before { content: "\f440"; }
.si-stellar.si--color::before { color: #7D00FF; }
.si-stencil::before { content: "\f441"; }
.si-stencil.si--color::before { color: #5530FF; }
.si-stencyl::before { content: "\f442"; }
.si-stencyl.si--color::before { color: #8E1C04; }
.si-stimulus::before { content: "\f443"; }
.si-stimulus.si--color::before { color: #77E8B9; }
.si-stitcher::before { content: "\f444"; }
.si-stitcher.si--color::before { color: #000000; }
.si-stmicroelectronics::before { content: "\f445"; }
.si-stmicroelectronics.si--color::before { color: #03234B; }
.si-stockx::before { content: "\f446"; }
.si-stockx.si--color::before { color: #006340; }
.si-stopstalk::before { content: "\f447"; }
.si-stopstalk.si--color::before { color: #536DFE; }
.si-storyblok::before { content: "\f448"; }
.si-storyblok.si--color::before { color: #09B3AF; }
.si-storybook::before { content: "\f449"; }
.si-storybook.si--color::before { color: #FF4785; }
.si-strapi::before { content: "\f44a"; }
.si-strapi.si--color::before { color: #4945FF; }
.si-strava::before { content: "\f44b"; }
.si-strava.si--color::before { color: #FC4C02; }
.si-streamlabs::before { content: "\f44c"; }
.si-streamlabs.si--color::before { color: #80F5D2; }
.si-streamlit::before { content: "\f44d"; }
.si-streamlit.si--color::before { color: #FF4B4B; }
.si-streamrunners::before { content: "\f44e"; }
.si-streamrunners.si--color::before { color: #6644F8; }
.si-stripe::before { content: "\f44f"; }
.si-stripe.si--color::before { color: #008CDD; }
.si-strongswan::before { content: "\f450"; }
.si-strongswan.si--color::before { color: #E00033; }
.si-stryker::before { content: "\f451"; }
.si-stryker.si--color::before { color: #E74C3C; }
.si-stubhub::before { content: "\f452"; }
.si-stubhub.si--color::before { color: #003168; }
.si-studio3t::before { content: "\f453"; }
.si-studio3t.si--color::before { color: #17AF66; }
.si-studyverse::before { content: "\f454"; }
.si-studyverse.si--color::before { color: #1D29E4; }
.si-styledcomponents::before { content: "\f455"; }
.si-styledcomponents.si--color::before { color: #DB7093; }
.si-stylelint::before { content: "\f456"; }
.si-stylelint.si--color::before { color: #263238; }
.si-styleshare::before { content: "\f457"; }
.si-styleshare.si--color::before { color: #212121; }
.si-stylus::before { content: "\f458"; }
.si-stylus.si--color::before { color: #333333; }
.si-subaru::before { content: "\f459"; }
.si-subaru.si--color::before { color: #013C74; }
.si-sublimetext::before { content: "\f45a"; }
.si-sublimetext.si--color::before { color: #FF9800; }
.si-substack::before { content: "\f45b"; }
.si-substack.si--color::before { color: #FF6719; }
.si-subtitleedit::before { content: "\f45c"; }
.si-subtitleedit.si--color::before { color: #CC2424; }
.si-subversion::before { content: "\f45d"; }
.si-subversion.si--color::before { color: #809CC9; }
.si-suckless::before { content: "\f45e"; }
.si-suckless.si--color::before { color: #1177AA; }
.si-sumologic::before { content: "\f45f"; }
.si-sumologic.si--color::before { color: #000099; }
.si-suno::before { content: "\f460"; }
.si-suno.si--color::before { color: #000000; }
.si-sunrise::before { content: "\f461"; }
.si-sunrise.si--color::before { color: #DA291C; }
.si-supabase::before { content: "\f462"; }
.si-supabase.si--color::before { color: #3FCF8E; }
.si-supercrease::before { content: "\f463"; }
.si-supercrease.si--color::before { color: #000000; }
.si-supermicro::before { content: "\f464"; }
.si-supermicro.si--color::before { color: #151F6D; }
.si-superuser::before { content: "\f465"; }
.si-superuser.si--color::before { color: #38A1CE; }
.si-surrealdb::before { content: "\f466"; }
.si-surrealdb.si--color::before { color: #FF00A0; }
.si-surveymonkey::before { content: "\f467"; }
.si-surveymonkey.si--color::before { color: #00BF6F; }
.si-suse::before { content: "\f468"; }
.si-suse.si--color::before { color: #0C322C; }
.si-suzuki::before { content: "\f469"; }
.si-suzuki.si--color::before { color: #E30613; }
.si-svelte::before { content: "\f46a"; }
.si-svelte.si--color::before { color: #FF3E00; }
.si-svg::before { content: "\f46b"; }
.si-svg.si--color::before { color: #FFB13B; }
.si-svgdotjs::before { content: "\f46c"; }
.si-svgdotjs.si--color::before { color: #FF0066; }
.si-svgo::before { content: "\f46d"; }
.si-svgo.si--color::before { color: #3E7FC1; }
.si-swagger::before { content: "\f46e"; }
.si-swagger.si--color::before { color: #85EA2D; }
.si-swarm::before { content: "\f46f"; }
.si-swarm.si--color::before { color: #FFA633; }
.si-swc::before { content: "\f470"; }
.si-swc.si--color::before { color: #F8C457; }
.si-swift::before { content: "\f471"; }
.si-swift.si--color::before { color: #F05138; }
.si-swiggy::before { content: "\f472"; }
.si-swiggy.si--color::before { color: #FC8019; }
.si-swiper::before { content: "\f473"; }
.si-swiper.si--color::before { color: #6332F6; }
.si-swr::before { content: "\f474"; }
.si-swr.si--color::before { color: #000000; }
.si-symantec::before { content: "\f475"; }
.si-symantec.si--color::before { color: #FDB511; }
.si-symbolab::before { content: "\f476"; }
.si-symbolab.si--color::before { color: #DB3F59; }
.si-symfony::before { content: "\f477"; }
.si-symfony.si--color::before { color: #000000; }
.si-symphony::before { content: "\f478"; }
.si-symphony.si--color::before { color: #0098FF; }
.si-sympy::before { content: "\f479"; }
.si-sympy.si--color::before { color: #3B5526; }
.si-syncthing::before { content: "\f47a"; }
.si-syncthing.si--color::before { color: #0891D1; }
.si-synology::before { content: "\f47b"; }
.si-synology.si--color::before { color: #B5B5B6; }
.si-system76::before { content: "\f47c"; }
.si-system76.si--color::before { color: #585048; }
.si-tabelog::before { content: "\f47d"; }
.si-tabelog.si--color::before { color: #F2CC38; }
.si-tableau::before { content: "\f47e"; }
.si-tableau.si--color::before { color: #E97627; }
.si-tablecheck::before { content: "\f47f"; }
.si-tablecheck.si--color::before { color: #7935D2; }
.si-tacobell::before { content: "\f480"; }
.si-tacobell.si--color::before { color: #38096C; }
.si-tado::before { content: "\f481"; }
.si-tado.si--color::before { color: #FFA900; }
.si-taichigraphics::before { content: "\f482"; }
.si-taichigraphics.si--color::before { color: #000000; }
.si-taichilang::before { content: "\f483"; }
.si-taichilang.si--color::before { color: #000000; }
.si-tails::before { content: "\f484"; }
.si-tails.si--color::before { color: #56347C; }
.si-tailscale::before { content: "\f485"; }
.si-tailscale.si--color::before { color: #242424; }
.si-tailwindcss::before { content: "\f486"; }
.si-tailwindcss.si--color::before { color: #06B6D4; }
.si-taipy::before { content: "\f487"; }
.si-taipy.si--color::before { color: #FF371A; }
.si-taketwointeractivesoftware::before { content: "\f488"; }
.si-taketwointeractivesoftware.si--color::before { color: #000000; }
.si-talend::before { content: "\f489"; }
.si-talend.si--color::before { color: #FF6D70; }
.si-talenthouse::before { content: "\f48a"; }
.si-talenthouse.si--color::before { color: #000000; }
.si-talos::before { content: "\f48b"; }
.si-talos.si--color::before { color: #FF7300; }
.si-tamiya::before { content: "\f48c"; }
.si-tamiya.si--color::before { color: #000000; }
.si-tampermonkey::before { content: "\f48d"; }
.si-tampermonkey.si--color::before { color: #00485B; }
.si-taobao::before { content: "\f48e"; }
.si-taobao.si--color::before { color: #E94F20; }
.si-tapas::before { content: "\f48f"; }
.si-tapas.si--color::before { color: #FFCE00; }
.si-target::before { content: "\f490"; }
.si-target.si--color::before { color: #CC0000; }
.si-task::before { content: "\f491"; }
.si-task.si--color::before { color: #29BEB0; }
.si-tasmota::before { content: "\f492"; }
.si-tasmota.si--color::before { color: #1FA3EC; }
.si-tata::before { content: "\f493"; }
.si-tata.si--color::before { color: #486AAE; }
.si-tauri::before { content: "\f494"; }
.si-tauri.si--color::before { color: #24C8D8; }
.si-taxbuzz::before { content: "\f495"; }
.si-taxbuzz.si--color::before { color: #ED8B0B; }
.si-tcs::before { content: "\f496"; }
.si-tcs.si--color::before { color: #EE3984; }
.si-teal::before { content: "\f497"; }
.si-teal.si--color::before { color: #005149; }
.si-teamcity::before { content: "\f498"; }
.si-teamcity.si--color::before { color: #000000; }
.si-teamspeak::before { content: "\f499"; }
.si-teamspeak.si--color::before { color: #4B69B6; }
.si-teamviewer::before { content: "\f49a"; }
.si-teamviewer.si--color::before { color: #004680; }
.si-techcrunch::before { content: "\f49b"; }
.si-techcrunch.si--color::before { color: #029F00; }
.si-ted::before { content: "\f49c"; }
.si-ted.si--color::before { color: #E62B1E; }
.si-teepublic::before { content: "\f49d"; }
.si-teepublic.si--color::before { color: #4E64DF; }
.si-teespring::before { content: "\f49e"; }
.si-teespring.si--color::before { color: #ED2761; }
.si-tekton::before { content: "\f49f"; }
.si-tekton.si--color::before { color: #FD495C; }
.si-tele5::before { content: "\f4a0"; }
.si-tele5.si--color::before { color: #FF00FF; }
.si-telegram::before { content: "\f4a1"; }
.si-telegram.si--color::before { color: #26A5E4; }
.si-telegraph::before { content: "\f4a2"; }
.si-telegraph.si--color::before { color: #FAFAFA; }
.si-telequebec::before { content: "\f4a3"; }
.si-telequebec.si--color::before { color: #1343FB; }
.si-temporal::before { content: "\f4a4"; }
.si-temporal.si--color::before { color: #000000; }
.si-tencentqq::before { content: "\f4a5"; }
.si-tencentqq.si--color::before { color: #1EBAFC; }
.si-tensorflow::before { content: "\f4a6"; }
.si-tensorflow.si--color::before { color: #FF6F00; }
.si-teradata::before { content: "\f4a7"; }
.si-teradata.si--color::before { color: #F37440; }
.si-teratail::before { content: "\f4a8"; }
.si-teratail.si--color::before { color: #F4C51C; }
.si-termius::before { content: "\f4a9"; }
.si-termius.si--color::before { color: #000000; }
.si-terraform::before { content: "\f4aa"; }
.si-terraform.si--color::before { color: #844FBA; }
.si-tesco::before { content: "\f4ab"; }
.si-tesco.si--color::before { color: #00539F; }
.si-tesla::before { content: "\f4ac"; }
.si-tesla.si--color::before { color: #CC0000; }
.si-testcafe::before { content: "\f4ad"; }
.si-testcafe.si--color::before { color: #36B6E5; }
.si-testin::before { content: "\f4ae"; }
.si-testin.si--color::before { color: #007DD7; }
.si-testinglibrary::before { content: "\f4af"; }
.si-testinglibrary.si--color::before { color: #E33332; }
.si-testrail::before { content: "\f4b0"; }
.si-testrail.si--color::before { color: #65C179; }
.si-tether::before { content: "\f4b1"; }
.si-tether.si--color::before { color: #50AF95; }
.si-textpattern::before { content: "\f4b2"; }
.si-textpattern.si--color::before { color: #FFDA44; }
.si-tga::before { content: "\f4b3"; }
.si-tga.si--color::before { color: #0014FF; }
.si-thangs::before { content: "\f4b4"; }
.si-thangs.si--color::before { color: #FFBC00; }
.si-thealgorithms::before { content: "\f4b5"; }
.si-thealgorithms.si--color::before { color: #00BCB4; }
.si-theboringcompany::before { content: "\f4b6"; }
.si-theboringcompany.si--color::before { color: #000000; }
.si-theconversation::before { content: "\f4b7"; }
.si-theconversation.si--color::before { color: #D8352A; }
.si-thefinals::before { content: "\f4b8"; }
.si-thefinals.si--color::before { color: #D31F3C; }
.si-theirishtimes::before { content: "\f4b9"; }
.si-theirishtimes.si--color::before { color: #000000; }
.si-themighty::before { content: "\f4ba"; }
.si-themighty.si--color::before { color: #D0072A; }
.si-themodelsresource::before { content: "\f4bb"; }
.si-themodelsresource.si--color::before { color: #3A75BD; }
.si-themoviedatabase::before { content: "\f4bc"; }
.si-themoviedatabase.si--color::before { color: #01B4E4; }
.si-thenorthface::before { content: "\f4bd"; }
.si-thenorthface.si--color::before { color: #000000; }
.si-theodinproject::before { content: "\f4be"; }
.si-theodinproject.si--color::before { color: #A9792B; }
.si-theregister::before { content: "\f4bf"; }
.si-theregister.si--color::before { color: #FF0000; }
.si-thesoundsresource::before { content: "\f4c0"; }
.si-thesoundsresource.si--color::before { color: #39BE6B; }
.si-thespritersresource::before { content: "\f4c1"; }
.si-thespritersresource.si--color::before { color: #BE3939; }
.si-thewashingtonpost::before { content: "\f4c2"; }
.si-thewashingtonpost.si--color::before { color: #231F20; }
.si-thingiverse::before { content: "\f4c3"; }
.si-thingiverse.si--color::before { color: #248BFB; }
.si-thinkpad::before { content: "\f4c4"; }
.si-thinkpad.si--color::before { color: #EE2624; }
.si-thirdweb::before { content: "\f4c5"; }
.si-thirdweb.si--color::before { color: #F213A4; }
.si-threadless::before { content: "\f4c6"; }
.si-threadless.si--color::before { color: #0099FF; }
.si-threads::before { content: "\f4c7"; }
.si-threads.si--color::before { color: #000000; }
.si-threedotjs::before { content: "\f4c8"; }
.si-threedotjs.si--color::before { color: #000000; }
.si-threema::before { content: "\f4c9"; }
.si-threema.si--color::before { color: #3FE669; }
.si-thumbtack::before { content: "\f4ca"; }
.si-thumbtack.si--color::before { color: #009FD9; }
.si-thunderbird::before { content: "\f4cb"; }
.si-thunderbird.si--color::before { color: #0A84FF; }
.si-thunderstore::before { content: "\f4cc"; }
.si-thunderstore.si--color::before { color: #23FFB0; }
.si-thurgauerkantonalbank::before { content: "\f4cd"; }
.si-thurgauerkantonalbank.si--color::before { color: #006D41; }
.si-thymeleaf::before { content: "\f4ce"; }
.si-thymeleaf.si--color::before { color: #005F0F; }
.si-ticketmaster::before { content: "\f4cf"; }
.si-ticketmaster.si--color::before { color: #026CDF; }
.si-ticktick::before { content: "\f4d0"; }
.si-ticktick.si--color::before { color: #4772FA; }
.si-tidal::before { content: "\f4d1"; }
.si-tidal.si--color::before { color: #000000; }
.si-tiddlywiki::before { content: "\f4d2"; }
.si-tiddlywiki.si--color::before { color: #111111; }
.si-tide::before { content: "\f4d3"; }
.si-tide.si--color::before { color: #4050FB; }
.si-tidyverse::before { content: "\f4d4"; }
.si-tidyverse.si--color::before { color: #1A162D; }
.si-tietoevry::before { content: "\f4d5"; }
.si-tietoevry.si--color::before { color: #063752; }
.si-tiktok::before { content: "\f4d6"; }
.si-tiktok.si--color::before { color: #000000; }
.si-tildapublishing::before { content: "\f4d7"; }
.si-tildapublishing.si--color::before { color: #FFA282; }
.si-tile::before { content: "\f4d8"; }
.si-tile.si--color::before { color: #000000; }
.si-timescale::before { content: "\f4d9"; }
.si-timescale.si--color::before { color: #FDB515; }
.si-tina::before { content: "\f4da"; }
.si-tina.si--color::before { color: #EC4815; }
.si-tinder::before { content: "\f4db"; }
.si-tinder.si--color::before { color: #FF6B6B; }
.si-tindie::before { content: "\f4dc"; }
.si-tindie.si--color::before { color: #17AEB9; }
.si-tinkercad::before { content: "\f4dd"; }
.si-tinkercad.si--color::before { color: #1477D1; }
.si-tinygrad::before { content: "\f4de"; }
.si-tinygrad.si--color::before { color: #FFFFFF; }
.si-tinyletter::before { content: "\f4df"; }
.si-tinyletter.si--color::before { color: #ED1C24; }
.si-tistory::before { content: "\f4e0"; }
.si-tistory.si--color::before { color: #000000; }
.si-tldraw::before { content: "\f4e1"; }
.si-tldraw.si--color::before { color: #FAFAFA; }
.si-tmobile::before { content: "\f4e2"; }
.si-tmobile.si--color::before { color: #E20074; }
.si-tmux::before { content: "\f4e3"; }
.si-tmux.si--color::before { color: #1BB91F; }
.si-todoist::before { content: "\f4e4"; }
.si-todoist.si--color::before { color: #E44332; }
.si-toggl::before { content: "\f4e5"; }
.si-toggl.si--color::before { color: #FFDE91; }
.si-toggltrack::before { content: "\f4e6"; }
.si-toggltrack.si--color::before { color: #E57CD8; }
.si-tokyometro::before { content: "\f4e7"; }
.si-tokyometro.si--color::before { color: #149DD3; }
.si-toll::before { content: "\f4e8"; }
.si-toll.si--color::before { color: #007A68; }
.si-toml::before { content: "\f4e9"; }
.si-toml.si--color::before { color: #9C4121; }
.si-tomorrowland::before { content: "\f4ea"; }
.si-tomorrowland.si--color::before { color: #000000; }
.si-ton::before { content: "\f4eb"; }
.si-ton.si--color::before { color: #0098EA; }
.si-topcoder::before { content: "\f4ec"; }
.si-topcoder.si--color::before { color: #29A7DF; }
.si-topdotgg::before { content: "\f4ed"; }
.si-topdotgg.si--color::before { color: #FF3366; }
.si-toptal::before { content: "\f4ee"; }
.si-toptal.si--color::before { color: #3863A0; }
.si-torbrowser::before { content: "\f4ef"; }
.si-torbrowser.si--color::before { color: #7D4698; }
.si-torproject::before { content: "\f4f0"; }
.si-torproject.si--color::before { color: #7D4698; }
.si-toshiba::before { content: "\f4f1"; }
.si-toshiba.si--color::before { color: #FF0000; }
.si-totvs::before { content: "\f4f2"; }
.si-totvs.si--color::before { color: #363636; }
.si-tourbox::before { content: "\f4f3"; }
.si-tourbox.si--color::before { color: #231F20; }
.si-tower::before { content: "\f4f4"; }
.si-tower.si--color::before { color: #00CAF4; }
.si-toyota::before { content: "\f4f5"; }
.si-toyota.si--color::before { color: #EB0A1E; }
.si-tplink::before { content: "\f4f6"; }
.si-tplink.si--color::before { color: #4ACBD6; }
.si-tqdm::before { content: "\f4f7"; }
.si-tqdm.si--color::before { color: #FFC107; }
.si-traccar::before { content: "\f4f8"; }
.si-traccar.si--color::before { color: #000000; }
.si-tradingview::before { content: "\f4f9"; }
.si-tradingview.si--color::before { color: #131622; }
.si-traefikmesh::before { content: "\f4fa"; }
.si-traefikmesh.si--color::before { color: #9D0FB0; }
.si-traefikproxy::before { content: "\f4fb"; }
.si-traefikproxy.si--color::before { color: #24A1C1; }
.si-trailforks::before { content: "\f4fc"; }
.si-trailforks.si--color::before { color: #FFCD00; }
.si-trainerroad::before { content: "\f4fd"; }
.si-trainerroad.si--color::before { color: #DA291C; }
.si-trakt::before { content: "\f4fe"; }
.si-trakt.si--color::before { color: #ED1C24; }
.si-transifex::before { content: "\f4ff"; }
.si-transifex.si--color::before { color: #0064AB; }
.si-transmission::before { content: "\f500"; }
.si-transmission.si--color::before { color: #D70008; }
.si-transportforireland::before { content: "\f501"; }
.si-transportforireland.si--color::before { color: #00B274; }
.si-transportforlondon::before { content: "\f502"; }
.si-transportforlondon.si--color::before { color: #113B92; }
.si-travisci::before { content: "\f503"; }
.si-travisci.si--color::before { color: #3EAAAF; }
.si-treehouse::before { content: "\f504"; }
.si-treehouse.si--color::before { color: #5FCF80; }
.si-trello::before { content: "\f505"; }
.si-trello.si--color::before { color: #0052CC; }
.si-trendmicro::before { content: "\f506"; }
.si-trendmicro.si--color::before { color: #D71921; }
.si-treyarch::before { content: "\f507"; }
.si-treyarch.si--color::before { color: #000000; }
.si-tricentis::before { content: "\f508"; }
.si-tricentis.si--color::before { color: #12438C; }
.si-trilium::before { content: "\f509"; }
.si-trilium.si--color::before { color: #000000; }
.si-triller::before { content: "\f50a"; }
.si-triller.si--color::before { color: #FF0089; }
.si-trillertv::before { content: "\f50b"; }
.si-trillertv.si--color::before { color: #E61414; }
.si-trino::before { content: "\f50c"; }
.si-trino.si--color::before { color: #DD00A1; }
.si-tripadvisor::before { content: "\f50d"; }
.si-tripadvisor.si--color::before { color: #34E0A1; }
.si-tripdotcom::before { content: "\f50e"; }
.si-tripdotcom.si--color::before { color: #287DFA; }
.si-trivago::before { content: "\f50f"; }
.si-trivago.si--color::before { color: #E32851; }
.si-trivy::before { content: "\f510"; }
.si-trivy.si--color::before { color: #1904DA; }
.si-trove::before { content: "\f511"; }
.si-trove.si--color::before { color: #2D004B; }
.si-trpc::before { content: "\f512"; }
.si-trpc.si--color::before { color: #2596BE; }
.si-truenas::before { content: "\f513"; }
.si-truenas.si--color::before { color: #0095D5; }
.si-trueup::before { content: "\f514"; }
.si-trueup.si--color::before { color: #4E71DA; }
.si-trulia::before { content: "\f515"; }
.si-trulia.si--color::before { color: #0A0B09; }
.si-trustedshops::before { content: "\f516"; }
.si-trustedshops.si--color::before { color: #FFDC0F; }
.si-trustpilot::before { content: "\f517"; }
.si-trustpilot.si--color::before { color: #00B67A; }
.si-tryhackme::before { content: "\f518"; }
.si-tryhackme.si--color::before { color: #212C42; }
.si-tryitonline::before { content: "\f519"; }
.si-tryitonline.si--color::before { color: #303030; }
.si-tsnode::before { content: "\f51a"; }
.si-tsnode.si--color::before { color: #3178C6; }
.si-tubi::before { content: "\f51b"; }
.si-tubi.si--color::before { color: #7408FF; }
.si-tui::before { content: "\f51c"; }
.si-tui.si--color::before { color: #D40E14; }
.si-tumblr::before { content: "\f51d"; }
.si-tumblr.si--color::before { color: #36465D; }
.si-tunein::before { content: "\f51e"; }
.si-tunein.si--color::before { color: #14D8CC; }
.si-turbo::before { content: "\f51f"; }
.si-turbo.si--color::before { color: #5CD8E5; }
.si-turborepo::before { content: "\f520"; }
.si-turborepo.si--color::before { color: #EF4444; }
.si-turbosquid::before { content: "\f521"; }
.si-turbosquid.si--color::before { color: #FF8135; }
.si-turkishairlines::before { content: "\f522"; }
.si-turkishairlines.si--color::before { color: #C70A0C; }
.si-turso::before { content: "\f523"; }
.si-turso.si--color::before { color: #4FF8D2; }
.si-tutanota::before { content: "\f524"; }
.si-tutanota.si--color::before { color: #840010; }
.si-tv4play::before { content: "\f525"; }
.si-tv4play.si--color::before { color: #E0001C; }
.si-tvtime::before { content: "\f526"; }
.si-tvtime.si--color::before { color: #FFD400; }
.si-twilio::before { content: "\f527"; }
.si-twilio.si--color::before { color: #F22F46; }
.si-twinkly::before { content: "\f528"; }
.si-twinkly.si--color::before { color: #FCC15E; }
.si-twinmotion::before { content: "\f529"; }
.si-twinmotion.si--color::before { color: #000000; }
.si-twitch::before { content: "\f52a"; }
.si-twitch.si--color::before { color: #9146FF; }
.si-typeform::before { content: "\f52b"; }
.si-typeform.si--color::before { color: #262627; }
.si-typeorm::before { content: "\f52c"; }
.si-typeorm.si--color::before { color: #FE0803; }
.si-typer::before { content: "\f52d"; }
.si-typer.si--color::before { color: #000000; }
.si-typescript::before { content: "\f52e"; }
.si-typescript.si--color::before { color: #3178C6; }
.si-typo3::before { content: "\f52f"; }
.si-typo3.si--color::before { color: #FF8700; }
.si-typst::before { content: "\f530"; }
.si-typst.si--color::before { color: #239DAD; }
.si-uber::before { content: "\f531"; }
.si-uber.si--color::before { color: #000000; }
.si-ubereats::before { content: "\f532"; }
.si-ubereats.si--color::before { color: #06C167; }
.si-ubiquiti::before { content: "\f533"; }
.si-ubiquiti.si--color::before { color: #0559C9; }
.si-ubisoft::before { content: "\f534"; }
.si-ubisoft.si--color::before { color: #000000; }
.si-ublockorigin::before { content: "\f535"; }
.si-ublockorigin.si--color::before { color: #800000; }
.si-ubuntu::before { content: "\f536"; }
.si-ubuntu.si--color::before { color: #E95420; }
.si-ubuntumate::before { content: "\f537"; }
.si-ubuntumate.si--color::before { color: #84A454; }
.si-udacity::before { content: "\f538"; }
.si-udacity.si--color::before { color: #02B3E4; }
.si-udemy::before { content: "\f539"; }
.si-udemy.si--color::before { color: #A435F0; }
.si-ufc::before { content: "\f53a"; }
.si-ufc.si--color::before { color: #D20A0A; }
.si-uikit::before { content: "\f53b"; }
.si-uikit.si--color::before { color: #2396F3; }
.si-uipath::before { content: "\f53c"; }
.si-uipath.si--color::before { color: #FA4616; }
.si-ulule::before { content: "\f53d"; }
.si-ulule.si--color::before { color: #18A5D6; }
.si-umami::before { content: "\f53e"; }
.si-umami.si--color::before { color: #000000; }
.si-umbraco::before { content: "\f53f"; }
.si-umbraco.si--color::before { color: #3544B1; }
.si-uml::before { content: "\f540"; }
.si-uml.si--color::before { color: #FABD14; }
.si-unacademy::before { content: "\f541"; }
.si-unacademy.si--color::before { color: #08BD80; }
.si-underarmour::before { content: "\f542"; }
.si-underarmour.si--color::before { color: #1D1D1D; }
.si-underscoredotjs::before { content: "\f543"; }
.si-underscoredotjs.si--color::before { color: #0371B5; }
.si-undertale::before { content: "\f544"; }
.si-undertale.si--color::before { color: #E71D29; }
.si-unicode::before { content: "\f545"; }
.si-unicode.si--color::before { color: #5455FE; }
.si-unilever::before { content: "\f546"; }
.si-unilever.si--color::before { color: #1F36C7; }
.si-uniqlo::before { content: "\f547"; }
.si-uniqlo.si--color::before { color: #FF0000; }
.si-uniqlo_ja::before { content: "\f548"; }
.si-uniqlo_ja.si--color::before { color: #FF0000; }
.si-unitedairlines::before { content: "\f549"; }
.si-unitedairlines.si--color::before { color: #002244; }
.si-unitednations::before { content: "\f54a"; }
.si-unitednations.si--color::before { color: #009EDB; }
.si-unity::before { content: "\f54b"; }
.si-unity.si--color::before { color: #FFFFFF; }
.si-unjs::before { content: "\f54c"; }
.si-unjs.si--color::before { color: #ECDC5A; }
.si-unlicense::before { content: "\f54d"; }
.si-unlicense.si--color::before { color: #808080; }
.si-unocss::before { content: "\f54e"; }
.si-unocss.si--color::before { color: #333333; }
.si-unpkg::before { content: "\f54f"; }
.si-unpkg.si--color::before { color: #000000; }
.si-unraid::before { content: "\f550"; }
.si-unraid.si--color::before { color: #F15A2C; }
.si-unrealengine::before { content: "\f551"; }
.si-unrealengine.si--color::before { color: #0E1128; }
.si-unsplash::before { content: "\f552"; }
.si-unsplash.si--color::before { color: #000000; }
.si-untappd::before { content: "\f553"; }
.si-untappd.si--color::before { color: #FFC000; }
.si-upcloud::before { content: "\f554"; }
.si-upcloud.si--color::before { color: #7B00FF; }
.si-uphold::before { content: "\f555"; }
.si-uphold.si--color::before { color: #49CC68; }
.si-uplabs::before { content: "\f556"; }
.si-uplabs.si--color::before { color: #3930D8; }
.si-upptime::before { content: "\f557"; }
.si-upptime.si--color::before { color: #1ABC9C; }
.si-ups::before { content: "\f558"; }
.si-ups.si--color::before { color: #150400; }
.si-upstash::before { content: "\f559"; }
.si-upstash.si--color::before { color: #00E9A3; }
.si-uptimekuma::before { content: "\f55a"; }
.si-uptimekuma.si--color::before { color: #5CDD8B; }
.si-uptobox::before { content: "\f55b"; }
.si-uptobox.si--color::before { color: #5CE1E6; }
.si-upwork::before { content: "\f55c"; }
.si-upwork.si--color::before { color: #6FDA44; }
.si-usps::before { content: "\f55d"; }
.si-usps.si--color::before { color: #333366; }
.si-utorrent::before { content: "\f55e"; }
.si-utorrent.si--color::before { color: #76B83F; }
.si-v::before { content: "\f55f"; }
.si-v.si--color::before { color: #5D87BF; }
.si-v2ex::before { content: "\f560"; }
.si-v2ex.si--color::before { color: #1F1F1F; }
.si-v8::before { content: "\f561"; }
.si-v8.si--color::before { color: #4B8BF5; }
.si-vaadin::before { content: "\f562"; }
.si-vaadin.si--color::before { color: #00B4F0; }
.si-vagrant::before { content: "\f563"; }
.si-vagrant.si--color::before { color: #1868F2; }
.si-vala::before { content: "\f564"; }
.si-vala.si--color::before { color: #7239B3; }
.si-valorant::before { content: "\f565"; }
.si-valorant.si--color::before { color: #FA4454; }
.si-valve::before { content: "\f566"; }
.si-valve.si--color::before { color: #F74843; }
.si-vapor::before { content: "\f567"; }
.si-vapor.si--color::before { color: #0D0D0D; }
.si-vault::before { content: "\f568"; }
.si-vault.si--color::before { color: #FFEC6E; }
.si-vaultwarden::before { content: "\f569"; }
.si-vaultwarden.si--color::before { color: #000000; }
.si-vauxhall::before { content: "\f56a"; }
.si-vauxhall.si--color::before { color: #EB001E; }
.si-vbulletin::before { content: "\f56b"; }
.si-vbulletin.si--color::before { color: #184D66; }
.si-vectary::before { content: "\f56c"; }
.si-vectary.si--color::before { color: #6100FF; }
.si-vectorlogozone::before { content: "\f56d"; }
.si-vectorlogozone.si--color::before { color: #184D66; }
.si-vectorworks::before { content: "\f56e"; }
.si-vectorworks.si--color::before { color: #000000; }
.si-veeam::before { content: "\f56f"; }
.si-veeam.si--color::before { color: #00B336; }
.si-veed::before { content: "\f570"; }
.si-veed.si--color::before { color: #B6FF60; }
.si-veepee::before { content: "\f571"; }
.si-veepee.si--color::before { color: #EC008C; }
.si-vega::before { content: "\f572"; }
.si-vega.si--color::before { color: #2450B2; }
.si-vegas::before { content: "\f573"; }
.si-vegas.si--color::before { color: #1A1A1A; }
.si-velog::before { content: "\f574"; }
.si-velog.si--color::before { color: #20C997; }
.si-venmo::before { content: "\f575"; }
.si-venmo.si--color::before { color: #008CFF; }
.si-vercel::before { content: "\f576"; }
.si-vercel.si--color::before { color: #000000; }
.si-verdaccio::before { content: "\f577"; }
.si-verdaccio.si--color::before { color: #4B5E40; }
.si-veritas::before { content: "\f578"; }
.si-veritas.si--color::before { color: #B1181E; }
.si-verizon::before { content: "\f579"; }
.si-verizon.si--color::before { color: #CD040B; }
.si-vespa::before { content: "\f57a"; }
.si-vespa.si--color::before { color: #85B09A; }
.si-vexxhost::before { content: "\f57b"; }
.si-vexxhost.si--color::before { color: #2A1659; }
.si-vfairs::before { content: "\f57c"; }
.si-vfairs.si--color::before { color: #EF4678; }
.si-viadeo::before { content: "\f57d"; }
.si-viadeo.si--color::before { color: #F07355; }
.si-viaplay::before { content: "\f57e"; }
.si-viaplay.si--color::before { color: #FE365F; }
.si-viber::before { content: "\f57f"; }
.si-viber.si--color::before { color: #7360F2; }
.si-viblo::before { content: "\f580"; }
.si-viblo.si--color::before { color: #5387C6; }
.si-victoriametrics::before { content: "\f581"; }
.si-victoriametrics.si--color::before { color: #621773; }
.si-victronenergy::before { content: "\f582"; }
.si-victronenergy.si--color::before { color: #0066B2; }
.si-vim::before { content: "\f583"; }
.si-vim.si--color::before { color: #019733; }
.si-vimeo::before { content: "\f584"; }
.si-vimeo.si--color::before { color: #1AB7EA; }
.si-vimeolivestream::before { content: "\f585"; }
.si-vimeolivestream.si--color::before { color: #0A0A20; }
.si-virgin::before { content: "\f586"; }
.si-virgin.si--color::before { color: #E10A0A; }
.si-virginatlantic::before { content: "\f587"; }
.si-virginatlantic.si--color::before { color: #DA0530; }
.si-virginmedia::before { content: "\f588"; }
.si-virginmedia.si--color::before { color: #ED1A37; }
.si-virtualbox::before { content: "\f589"; }
.si-virtualbox.si--color::before { color: #183A61; }
.si-virustotal::before { content: "\f58a"; }
.si-virustotal.si--color::before { color: #394EFF; }
.si-visa::before { content: "\f58b"; }
.si-visa.si--color::before { color: #1A1F71; }
.si-visx::before { content: "\f58c"; }
.si-visx.si--color::before { color: #FF1231; }
.si-vite::before { content: "\f58d"; }
.si-vite.si--color::before { color: #646CFF; }
.si-vitepress::before { content: "\f58e"; }
.si-vitepress.si--color::before { color: #5C73E7; }
.si-vitess::before { content: "\f58f"; }
.si-vitess.si--color::before { color: #F16728; }
.si-vitest::before { content: "\f590"; }
.si-vitest.si--color::before { color: #6E9F18; }
.si-vivaldi::before { content: "\f591"; }
.si-vivaldi.si--color::before { color: #EF3939; }
.si-vivawallet::before { content: "\f592"; }
.si-vivawallet.si--color::before { color: #1F263A; }
.si-vivino::before { content: "\f593"; }
.si-vivino.si--color::before { color: #A61A30; }
.si-vivint::before { content: "\f594"; }
.si-vivint.si--color::before { color: #212721; }
.si-vivo::before { content: "\f595"; }
.si-vivo.si--color::before { color: #415FFF; }
.si-vk::before { content: "\f596"; }
.si-vk.si--color::before { color: #0077FF; }
.si-vlcmediaplayer::before { content: "\f597"; }
.si-vlcmediaplayer.si--color::before { color: #FF8800; }
.si-vmware::before { content: "\f598"; }
.si-vmware.si--color::before { color: #607078; }
.si-vodafone::before { content: "\f599"; }
.si-vodafone.si--color::before { color: #E60000; }
.si-voidlinux::before { content: "\f59a"; }
.si-voidlinux.si--color::before { color: #478061; }
.si-voipdotms::before { content: "\f59b"; }
.si-voipdotms.si--color::before { color: #E1382D; }
.si-volkswagen::before { content: "\f59c"; }
.si-volkswagen.si--color::before { color: #151F5D; }
.si-volvo::before { content: "\f59d"; }
.si-volvo.si--color::before { color: #003057; }
.si-vonage::before { content: "\f59e"; }
.si-vonage.si--color::before { color: #000000; }
.si-vorondesign::before { content: "\f59f"; }
.si-vorondesign.si--color::before { color: #ED3023; }
.si-vowpalwabbit::before { content: "\f5a0"; }
.si-vowpalwabbit.si--color::before { color: #FF81F9; }
.si-vox::before { content: "\f5a1"; }
.si-vox.si--color::before { color: #DA074A; }
.si-vsco::before { content: "\f5a2"; }
.si-vsco.si--color::before { color: #000000; }
.si-vscodium::before { content: "\f5a3"; }
.si-vscodium.si--color::before { color: #2F80ED; }
.si-vtex::before { content: "\f5a4"; }
.si-vtex.si--color::before { color: #ED125F; }
.si-vuedotjs::before { content: "\f5a5"; }
.si-vuedotjs.si--color::before { color: #4FC08D; }
.si-vuetify::before { content: "\f5a6"; }
.si-vuetify.si--color::before { color: #1867C0; }
.si-vulkan::before { content: "\f5a7"; }
.si-vulkan.si--color::before { color: #A41E22; }
.si-vultr::before { content: "\f5a8"; }
.si-vultr.si--color::before { color: #007BFC; }
.si-vyond::before { content: "\f5a9"; }
.si-vyond.si--color::before { color: #D95E26; }
.si-w3schools::before { content: "\f5aa"; }
.si-w3schools.si--color::before { color: #04AA6D; }
.si-wacom::before { content: "\f5ab"; }
.si-wacom.si--color::before { color: #000000; }
.si-wagmi::before { content: "\f5ac"; }
.si-wagmi.si--color::before { color: #000000; }
.si-wagtail::before { content: "\f5ad"; }
.si-wagtail.si--color::before { color: #43B1B0; }
.si-wails::before { content: "\f5ae"; }
.si-wails.si--color::before { color: #DF0000; }
.si-wakatime::before { content: "\f5af"; }
.si-wakatime.si--color::before { color: #000000; }
.si-walkman::before { content: "\f5b0"; }
.si-walkman.si--color::before { color: #000000; }
.si-wallabag::before { content: "\f5b1"; }
.si-wallabag.si--color::before { color: #3F6184; }
.si-walletconnect::before { content: "\f5b2"; }
.si-walletconnect.si--color::before { color: #3B99FC; }
.si-walmart::before { content: "\f5b3"; }
.si-walmart.si--color::before { color: #0071CE; }
.si-wantedly::before { content: "\f5b4"; }
.si-wantedly.si--color::before { color: #21BDDB; }
.si-wappalyzer::before { content: "\f5b5"; }
.si-wappalyzer.si--color::before { color: #4608AD; }
.si-warnerbros::before { content: "\f5b6"; }
.si-warnerbros.si--color::before { color: #004DB4; }
.si-warp::before { content: "\f5b7"; }
.si-warp.si--color::before { color: #01A4FF; }
.si-wasabi::before { content: "\f5b8"; }
.si-wasabi.si--color::before { color: #01CD3E; }
.si-wasmcloud::before { content: "\f5b9"; }
.si-wasmcloud.si--color::before { color: #00BC8E; }
.si-wasmer::before { content: "\f5ba"; }
.si-wasmer.si--color::before { color: #4946DD; }
.si-watchtower::before { content: "\f5bb"; }
.si-watchtower.si--color::before { color: #416271; }
.si-wattpad::before { content: "\f5bc"; }
.si-wattpad.si--color::before { color: #FF500A; }
.si-wayland::before { content: "\f5bd"; }
.si-wayland.si--color::before { color: #FFBC00; }
.si-waze::before { content: "\f5be"; }
.si-waze.si--color::before { color: #33CCFF; }
.si-wazirx::before { content: "\f5bf"; }
.si-wazirx.si--color::before { color: #3067F0; }
.si-wearos::before { content: "\f5c0"; }
.si-wearos.si--color::before { color: #4285F4; }
.si-weasyl::before { content: "\f5c1"; }
.si-weasyl.si--color::before { color: #990000; }
.si-web3dotjs::before { content: "\f5c2"; }
.si-web3dotjs.si--color::before { color: #F16822; }
.si-webassembly::before { content: "\f5c3"; }
.si-webassembly.si--color::before { color: #654FF0; }
.si-webauthn::before { content: "\f5c4"; }
.si-webauthn.si--color::before { color: #3423A6; }
.si-webcomponentsdotorg::before { content: "\f5c5"; }
.si-webcomponentsdotorg.si--color::before { color: #29ABE2; }
.si-webdriverio::before { content: "\f5c6"; }
.si-webdriverio.si--color::before { color: #EA5906; }
.si-webex::before { content: "\f5c7"; }
.si-webex.si--color::before { color: #000000; }
.si-webflow::before { content: "\f5c8"; }
.si-webflow.si--color::before { color: #146EF5; }
.si-webgl::before { content: "\f5c9"; }
.si-webgl.si--color::before { color: #990000; }
.si-webgpu::before { content: "\f5ca"; }
.si-webgpu.si--color::before { color: #005A9C; }
.si-weblate::before { content: "\f5cb"; }
.si-weblate.si--color::before { color: #2ECCAA; }
.si-webmin::before { content: "\f5cc"; }
.si-webmin.si--color::before { color: #7DA0D0; }
.si-webmoney::before { content: "\f5cd"; }
.si-webmoney.si--color::before { color: #036CB5; }
.si-webpack::before { content: "\f5ce"; }
.si-webpack.si--color::before { color: #8DD6F9; }
.si-webrtc::before { content: "\f5cf"; }
.si-webrtc.si--color::before { color: #333333; }
.si-webstorm::before { content: "\f5d0"; }
.si-webstorm.si--color::before { color: #000000; }
.si-webtoon::before { content: "\f5d1"; }
.si-webtoon.si--color::before { color: #00D564; }
.si-webtrees::before { content: "\f5d2"; }
.si-webtrees.si--color::before { color: #2694E8; }
.si-wechat::before { content: "\f5d3"; }
.si-wechat.si--color::before { color: #07C160; }
.si-wegame::before { content: "\f5d4"; }
.si-wegame.si--color::before { color: #FAAB00; }
.si-weightsandbiases::before { content: "\f5d5"; }
.si-weightsandbiases.si--color::before { color: #FFBE00; }
.si-welcometothejungle::before { content: "\f5d6"; }
.si-welcometothejungle.si--color::before { color: #FFCD00; }
.si-wellfound::before { content: "\f5d7"; }
.si-wellfound.si--color::before { color: #000000; }
.si-wellsfargo::before { content: "\f5d8"; }
.si-wellsfargo.si--color::before { color: #D71E28; }
.si-wemo::before { content: "\f5d9"; }
.si-wemo.si--color::before { color: #72D44C; }
.si-westerndigital::before { content: "\f5da"; }
.si-westerndigital.si--color::before { color: #995DFF; }
.si-westernunion::before { content: "\f5db"; }
.si-westernunion.si--color::before { color: #FFDD00; }
.si-wetransfer::before { content: "\f5dc"; }
.si-wetransfer.si--color::before { color: #409FFF; }
.si-wezterm::before { content: "\f5dd"; }
.si-wezterm.si--color::before { color: #4E49EE; }
.si-wgpu::before { content: "\f5de"; }
.si-wgpu.si--color::before { color: #40E0D0; }
.si-whatsapp::before { content: "\f5df"; }
.si-whatsapp.si--color::before { color: #25D366; }
.si-wheniwork::before { content: "\f5e0"; }
.si-wheniwork.si--color::before { color: #51A33D; }
.si-wii::before { content: "\f5e1"; }
.si-wii.si--color::before { color: #8B8B8B; }
.si-wiiu::before { content: "\f5e2"; }
.si-wiiu.si--color::before { color: #8B8B8B; }
.si-wikidata::before { content: "\f5e3"; }
.si-wikidata.si--color::before { color: #006699; }
.si-wikidotgg::before { content: "\f5e4"; }
.si-wikidotgg.si--color::before { color: #FF1985; }
.si-wikidotjs::before { content: "\f5e5"; }
.si-wikidotjs.si--color::before { color: #1976D2; }
.si-wikimediacommons::before { content: "\f5e6"; }
.si-wikimediacommons.si--color::before { color: #006699; }
.si-wikimediafoundation::before { content: "\f5e7"; }
.si-wikimediafoundation.si--color::before { color: #000000; }
.si-wikipedia::before { content: "\f5e8"; }
.si-wikipedia.si--color::before { color: #000000; }
.si-wikiquote::before { content: "\f5e9"; }
.si-wikiquote.si--color::before { color: #006699; }
.si-wikivoyage::before { content: "\f5ea"; }
.si-wikivoyage.si--color::before { color: #006699; }
.si-winamp::before { content: "\f5eb"; }
.si-winamp.si--color::before { color: #F93821; }
.si-wipro::before { content: "\f5ec"; }
.si-wipro.si--color::before { color: #341C53; }
.si-wire::before { content: "\f5ed"; }
.si-wire.si--color::before { color: #000000; }
.si-wireguard::before { content: "\f5ee"; }
.si-wireguard.si--color::before { color: #88171A; }
.si-wireshark::before { content: "\f5ef"; }
.si-wireshark.si--color::before { color: #1679A7; }
.si-wise::before { content: "\f5f0"; }
.si-wise.si--color::before { color: #9FE870; }
.si-wish::before { content: "\f5f1"; }
.si-wish.si--color::before { color: #32E476; }
.si-wistia::before { content: "\f5f2"; }
.si-wistia.si--color::before { color: #58B7FE; }
.si-wix::before { content: "\f5f3"; }
.si-wix.si--color::before { color: #0C6EFC; }
.si-wizzair::before { content: "\f5f4"; }
.si-wizzair.si--color::before { color: #C6007E; }
.si-wolfram::before { content: "\f5f5"; }
.si-wolfram.si--color::before { color: #DD1100; }
.si-wolframlanguage::before { content: "\f5f6"; }
.si-wolframlanguage.si--color::before { color: #DD1100; }
.si-wolframmathematica::before { content: "\f5f7"; }
.si-wolframmathematica.si--color::before { color: #DD1100; }
.si-wondershare::before { content: "\f5f8"; }
.si-wondershare.si--color::before { color: #000000; }
.si-wondersharefilmora::before { content: "\f5f9"; }
.si-wondersharefilmora.si--color::before { color: #07273D; }
.si-woo::before { content: "\f5fa"; }
.si-woo.si--color::before { color: #96588A; }
.si-woocommerce::before { content: "\f5fb"; }
.si-woocommerce.si--color::before { color: #96588A; }
.si-wordpress::before { content: "\f5fc"; }
.si-wordpress.si--color::before { color: #21759B; }
.si-workplace::before { content: "\f5fd"; }
.si-workplace.si--color::before { color: #4526CE; }
.si-worldhealthorganization::before { content: "\f5fe"; }
.si-worldhealthorganization.si--color::before { color: #0093D5; }
.si-wpengine::before { content: "\f5ff"; }
.si-wpengine.si--color::before { color: #0ECAD4; }
.si-wpexplorer::before { content: "\f600"; }
.si-wpexplorer.si--color::before { color: #2563EB; }
.si-wprocket::before { content: "\f601"; }
.si-wprocket.si--color::before { color: #F56640; }
.si-writedotas::before { content: "\f602"; }
.si-writedotas.si--color::before { color: #5AC4EE; }
.si-wwe::before { content: "\f603"; }
.si-wwe.si--color::before { color: #000000; }
.si-wwise::before { content: "\f604"; }
.si-wwise.si--color::before { color: #00549F; }
.si-wykop::before { content: "\f605"; }
.si-wykop.si--color::before { color: #367DA9; }
.si-wyze::before { content: "\f606"; }
.si-wyze.si--color::before { color: #1DF0BB; }
.si-x::before { content: "\f607"; }
.si-x.si--color::before { color: #000000; }
.si-xampp::before { content: "\f608"; }
.si-xampp.si--color::before { color: #FB7A24; }
.si-xcode::before { content: "\f609"; }
.si-xcode.si--color::before { color: #147EFB; }
.si-xdadevelopers::before { content: "\f60a"; }
.si-xdadevelopers.si--color::before { color: #EA7100; }
.si-xdotorg::before { content: "\f60b"; }
.si-xdotorg.si--color::before { color: #F28834; }
.si-xendit::before { content: "\f60c"; }
.si-xendit.si--color::before { color: #4573FF; }
.si-xero::before { content: "\f60d"; }
.si-xero.si--color::before { color: #13B5EA; }
.si-xfce::before { content: "\f60e"; }
.si-xfce.si--color::before { color: #2284F2; }
.si-xiaohongshu::before { content: "\f60f"; }
.si-xiaohongshu.si--color::before { color: #FF2442; }
.si-xiaomi::before { content: "\f610"; }
.si-xiaomi.si--color::before { color: #FF6900; }
.si-xing::before { content: "\f611"; }
.si-xing.si--color::before { color: #006567; }
.si-xmpp::before { content: "\f612"; }
.si-xmpp.si--color::before { color: #002B5C; }
.si-xo::before { content: "\f613"; }
.si-xo.si--color::before { color: #5ED9C7; }
.si-xrp::before { content: "\f614"; }
.si-xrp.si--color::before { color: #25A768; }
.si-xsplit::before { content: "\f615"; }
.si-xsplit.si--color::before { color: #0095DE; }
.si-xstate::before { content: "\f616"; }
.si-xstate.si--color::before { color: #2C3E50; }
.si-yabai::before { content: "\f617"; }
.si-yabai.si--color::before { color: #00364B; }
.si-yale::before { content: "\f618"; }
.si-yale.si--color::before { color: #FFD900; }
.si-yamahacorporation::before { content: "\f619"; }
.si-yamahacorporation.si--color::before { color: #4B1E78; }
.si-yamahamotorcorporation::before { content: "\f61a"; }
.si-yamahamotorcorporation.si--color::before { color: #E60012; }
.si-yaml::before { content: "\f61b"; }
.si-yaml.si--color::before { color: #CB171E; }
.si-yandexcloud::before { content: "\f61c"; }
.si-yandexcloud.si--color::before { color: #5282FF; }
.si-yarn::before { content: "\f61d"; }
.si-yarn.si--color::before { color: #2C8EBB; }
.si-ycombinator::before { content: "\f61e"; }
.si-ycombinator.si--color::before { color: #F0652F; }
.si-yelp::before { content: "\f61f"; }
.si-yelp.si--color::before { color: #FF1A1A; }
.si-yeti::before { content: "\f620"; }
.si-yeti.si--color::before { color: #00263C; }
.si-yoast::before { content: "\f621"; }
.si-yoast.si--color::before { color: #A61E69; }
.si-youtube::before { content: "\f622"; }
.si-youtube.si--color::before { color: #FF0000; }
.si-youtubegaming::before { content: "\f623"; }
.si-youtubegaming.si--color::before { color: #FF0000; }
.si-youtubekids::before { content: "\f624"; }
.si-youtubekids.si--color::before { color: #FF0000; }
.si-youtubemusic::before { content: "\f625"; }
.si-youtubemusic.si--color::before { color: #FF0000; }
.si-youtubeshorts::before { content: "\f626"; }
.si-youtubeshorts.si--color::before { color: #FF0000; }
.si-youtubestudio::before { content: "\f627"; }
.si-youtubestudio.si--color::before { color: #FF0000; }
.si-youtubetv::before { content: "\f628"; }
.si-youtubetv.si--color::before { color: #FF0000; }
.si-yr::before { content: "\f629"; }
.si-yr.si--color::before { color: #00B9F1; }
.si-yubico::before { content: "\f62a"; }
.si-yubico.si--color::before { color: #84BD00; }
.si-yunohost::before { content: "\f62b"; }
.si-yunohost.si--color::before { color: #000000; }
.si-zabka::before { content: "\f62c"; }
.si-zabka.si--color::before { color: #006420; }
.si-zaim::before { content: "\f62d"; }
.si-zaim.si--color::before { color: #50A135; }
.si-zalando::before { content: "\f62e"; }
.si-zalando.si--color::before { color: #FF6900; }
.si-zalo::before { content: "\f62f"; }
.si-zalo.si--color::before { color: #0068FF; }
.si-zap::before { content: "\f630"; }
.si-zap.si--color::before { color: #00549E; }
.si-zapier::before { content: "\f631"; }
.si-zapier.si--color::before { color: #FF4F00; }
.si-zara::before { content: "\f632"; }
.si-zara.si--color::before { color: #000000; }
.si-zazzle::before { content: "\f633"; }
.si-zazzle.si--color::before { color: #212121; }
.si-zcash::before { content: "\f634"; }
.si-zcash.si--color::before { color: #F3B724; }
.si-zcool::before { content: "\f635"; }
.si-zcool.si--color::before { color: #FFF200; }
.si-zdf::before { content: "\f636"; }
.si-zdf.si--color::before { color: #FA7D19; }
.si-zebpay::before { content: "\f637"; }
.si-zebpay.si--color::before { color: #2072EF; }
.si-zebratechnologies::before { content: "\f638"; }
.si-zebratechnologies.si--color::before { color: #000000; }
.si-zedindustries::before { content: "\f639"; }
.si-zedindustries.si--color::before { color: #084CCF; }
.si-zelle::before { content: "\f63a"; }
.si-zelle.si--color::before { color: #6D1ED4; }
.si-zend::before { content: "\f63b"; }
.si-zend.si--color::before { color: #0679EA; }
.si-zendesk::before { content: "\f63c"; }
.si-zendesk.si--color::before { color: #03363D; }
.si-zenn::before { content: "\f63d"; }
.si-zenn.si--color::before { color: #3EA8FF; }
.si-zenodo::before { content: "\f63e"; }
.si-zenodo.si--color::before { color: #1682D4; }
.si-zensar::before { content: "\f63f"; }
.si-zensar.si--color::before { color: #000000; }
.si-zerodha::before { content: "\f640"; }
.si-zerodha.si--color::before { color: #387ED1; }
.si-zerotier::before { content: "\f641"; }
.si-zerotier.si--color::before { color: #FFB441; }
.si-zerply::before { content: "\f642"; }
.si-zerply.si--color::before { color: #7BBB6E; }
.si-zettlr::before { content: "\f643"; }
.si-zettlr.si--color::before { color: #1CB27E; }
.si-zhihu::before { content: "\f644"; }
.si-zhihu.si--color::before { color: #0084FF; }
.si-zig::before { content: "\f645"; }
.si-zig.si--color::before { color: #F7A41D; }
.si-zigbee::before { content: "\f646"; }
.si-zigbee.si--color::before { color: #EB0443; }
.si-zigbee2mqtt::before { content: "\f647"; }
.si-zigbee2mqtt.si--color::before { color: #FFC135; }
.si-ziggo::before { content: "\f648"; }
.si-ziggo.si--color::before { color: #F48C00; }
.si-zilch::before { content: "\f649"; }
.si-zilch.si--color::before { color: #00D287; }
.si-zillow::before { content: "\f64a"; }
.si-zillow.si--color::before { color: #006AFF; }
.si-zincsearch::before { content: "\f64b"; }
.si-zincsearch.si--color::before { color: #5BA37F; }
.si-zingat::before { content: "\f64c"; }
.si-zingat.si--color::before { color: #009CFB; }
.si-zod::before { content: "\f64d"; }
.si-zod.si--color::before { color: #3E67B1; }
.si-zoho::before { content: "\f64e"; }
.si-zoho.si--color::before { color: #E42527; }
.si-zoiper::before { content: "\f64f"; }
.si-zoiper.si--color::before { color: #F47920; }
.si-zomato::before { content: "\f650"; }
.si-zomato.si--color::before { color: #E23744; }
.si-zoom::before { content: "\f651"; }
.si-zoom.si--color::before { color: #0B5CFF; }
.si-zorin::before { content: "\f652"; }
.si-zorin.si--color::before { color: #15A6F0; }
.si-zotero::before { content: "\f653"; }
.si-zotero.si--color::before { color: #CC2936; }
.si-zsh::before { content: "\f654"; }
.si-zsh.si--color::before { color: #F15A24; }
.si-zulip::before { content: "\f655"; }
.si-zulip.si--color::before { color: #6492FE; }
.si-zyte::before { content: "\f656"; }
.si-zyte.si--color::before { color: #B02CCE; }
