+++
title = "About"
description = "<PERSON>, the world’s fastest framework for building websites"
date = "2019-02-28"
aliases = ["about-us","about-hugo","contact"]
author = "<PERSON> Authors"
+++

Written in Go, <PERSON> is an open source static site generator available under the [Apache Licence 2.0.](https://github.com/gohugoio/hugo/blob/master/LICENSE) <PERSON> supports TOML, YAML and JSON data file types, Markdown and HTML content files and uses shortcodes to add rich content. Other notable features are taxonomies, multilingual mode, image processing, custom output formats, HTML/CSS/JS minification and support for Sass SCSS workflows.

<PERSON> makes use of a variety of open source projects including:

* https://github.com/yuin/goldmark
* https://github.com/alecthomas/chroma
* https://github.com/muesli/smartcrop
* https://github.com/spf13/cobra
* https://github.com/spf13/viper

<PERSON> is ideal for blogs, corporate websites, creative portfolios, online magazines, single page applications or even a website with thousands of pages.

<PERSON> is for people who want to hand code their own website without worrying about setting up complicated runtimes, dependencies and databases.

Websites built with <PERSON> are extremelly fast, secure and can be deployed anywhere including, AWS, GitHub Pages, Heroku, Netlify and any other hosting provider.

Learn more and contribute on [GitHub](https://github.com/gohugoio).



