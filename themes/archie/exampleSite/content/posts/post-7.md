---
title: "How to test dark mode?"
summary: "Here is how you can setup dark mode for Ink and test on various OS like iOS, Android, macOS and Windows 10."
date: 2018-03-18T12:13:38+05:30
tldr: "Wubba lubba dub dub"
---

You can set dark mode as default by setting `params.mode` to `dark` in `config.toml` or set it to `auto` which will detect based on your OS and switch to dark mode. For more details [refer documentation](https://github.com/knadh/hugo-ink#configuration)

Here is how you can switch based on your OS

* [iOS](https://www.howtogeek.com/440078/how-to-enable-dark-mode-on-your-iphone-and-ipad/)
* [Android](https://9to5google.com/2018/12/17/android-dark-mode-theme-pie/)
* [macOS](https://support.apple.com/en-in/HT208976)
* [Windows 10](https://www.cnet.com/how-to/turn-on-the-dark-mode-in-windows-10/)
