# theme.toml template for a Hugo theme
# See https://github.com/gohugoio/hugoThemes#themetoml for an example

name = "Archie"
license = "MIT"
licenselink = "https://github.com/athul/archie/blob/master/LICENSE"
description = "<PERSON> is a minimal and clean theme for hugo with a markdown-ish UI."
homepage = "https://github.com/athul/archie"
tags = ["blog","simple","responsive","minimal","tags","personal","clean","shortcodes"]
features = ["blog", "Clean and minimal", "Responsive", "Syntax highlighting",]
min_version = "0.41"

[author]
  name = "Athul Cyriac Ajay"
  homepage = "https://github.com/athul"
