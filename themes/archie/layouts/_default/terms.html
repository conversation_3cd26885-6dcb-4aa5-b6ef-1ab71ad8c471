{{ define "main" }}
<h1 class="page-title">All tags</h1>

{{ $biggest := 1 }}
{{ $smallest := 1 }}
{{ $max := 3 }}
{{ $min := 1 }}
{{ $size := $min }}

{{ $data := .Data }}
<div class="tag-cloud">
	<ul class="tags">
		{{ range $key, $value := .Data.Terms.ByCount }}
		{{ $size := (add (mul (div $value.Count $biggest) (sub $max $min)) $min) }}
		{{ $size := (cond (eq $biggest $smallest) $min $size) }}
		<li><a style="font-size: {{ $size }}rem;" href="{{ $.Site.LanguagePrefix | absURL }}{{ $data.Plural }}/{{ $value.Name | urlize }}/">{{ $value.Name }}</a></li>
		{{ end }}
	</ul>
</div>
{{ end }}
