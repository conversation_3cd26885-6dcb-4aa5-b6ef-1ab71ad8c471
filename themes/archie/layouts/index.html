<!DOCTYPE html>
<html>
	{{ partial "header.html" . }}
	<body>
		<div class="content">
			{{ partial "head.html" . }}
			
			<main class="list">
				<div class="site-description">
					{{- if isset .Site.Params "subtitle" -}}
					<p>{{ .Site.Params.Subtitle | .Page.RenderString }}</p>
					{{- end -}}
				</div>
				{{ $pages := where .Site.RegularPages "Type" "in" .Site.Params.mainSections }}
				{{ $paginator := .Paginate (where $pages "Params.hidden" "ne" true) }}
				{{ range $paginator.Pages }}
				<section class="list-item">
					<h1 class="title"><a href="{{ .RelPermalink }}">{{.Title}}</a></h1>
					<time>{{ dateFormat ":date_medium" .Date }}{{ if .Draft }} <span class="draft-label">DRAFT</span> {{ end }}</time>
					<br>{{ template "partials/pagedescription.html" . }}
					<a class="readmore" href="{{ .RelPermalink }}">Read more ⟶</a>
				</section>
				{{ end }}
				{{ template "partials/paginator.html" . }}
			</main>
			{{ partial "footer.html" . }}
		</div>
		
	</body>
</html>
