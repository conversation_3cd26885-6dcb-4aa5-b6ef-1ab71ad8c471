{{- define "feathericon" -}}
  {{- $featherURL := "https://unpkg.com/feather-icons@4.29.2/dist/feather-sprite.svg" -}}
  {{ if not (.UseCDN | default false) -}}
    {{- $featherURL = (resources.Get "svg/feather-sprite.svg" | fingerprint).RelPermalink -}}
  {{- end -}}
<svg class="feather">
   <use href="{{ printf "%s#%s" $featherURL .Icon }}" />
</svg>
{{- end -}}

<header>
	<div class="main">
		<a href="{{ absLangURL "/" }}">{{ .Site.Title }}</a>
	</div>
	<nav>
		{{ range .Site.Menus.main }}
		<a href="{{ .URL }}">{{ .Name }}</a>
		{{ end }}
		{{ if eq .Site.Params.mode "toggle" -}}
	| <span id="dark-mode-toggle" onclick="toggleTheme()">{{template "feathericon" (dict "UseCDN" .Site.Params.useCDN "Icon" "sun") }}</span>
		<script src="{{ absURL "js/themetoggle.js" }}"></script>
		{{ end }}
	</nav>
</header>
