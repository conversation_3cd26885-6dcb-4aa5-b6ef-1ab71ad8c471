<head lang="{{ .Site.Language.Lang }}">
	<meta charset="utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	{{- $title := ( .Title ) -}}
	{{- $siteTitle := ( .Site.Title ) -}}
	{{- if .IsHome -}}
	<title>{{ $siteTitle }} | Home </title>
	{{- else -}}
	<title>{{ $title }} - {{ $siteTitle }}</title>
	{{- end -}}

	{{- if isset .Site.Params "favicon" -}}
	<link rel="icon" type="image/png" href={{ .Site.Params.favicon }} />
	{{- end -}}

	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="description" content="{{ with .Description }}{{ . }}{{ else }}{{if .IsPage}}{{ .Summary }}{{ else }}{{ with .Site.Params.description }}{{ . }}{{ end }}{{ end }}{{ end }}" />
	<meta property="og:image" content="{{ .Site.Params.og_image }}"/>
	{{ with .OutputFormats.Get "rss" -}}
	{{ printf `<link rel="%s" type="%s" href="%s" title="%s" />` .Rel .MediaType.Type .Permalink $.Site.Title | safeHTML }}
	{{ end -}}

	{{- template "_internal/opengraph.html" . -}}
	{{- template "_internal/twitter_cards.html" . -}}
	{{- if isset .Site.Params "social" -}}
	  {{- range $index, $key := .Site.Params.Social -}}
	    {{- if hasPrefix $key.icon "simple:" -}}
	      {{- if (.Site.Params.useCDN | default false) -}}
		<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/simple-icons-font@v13/font/simple-icons.min.css" type="text/css">
	      {{- else -}}
		<link rel="stylesheet" href="{{ (resources.Get "css/simple-icons.css" | fingerprint).Permalink }}" type="text/css">
	      {{- end -}}
	    {{- end -}}
	  {{- end -}}
	{{- end -}}
	{{ if .Site.Params.useCDN | default false -}}
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:ital,wght@1,500&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Fira+Sans&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Roboto+Mono" rel="stylesheet">
	{{- else -}}
	{{ $fontstyle := resources.Get "css/fonts.css" | fingerprint }}
        <link href="{{ $fontstyle.Permalink }}" rel="stylesheet">
	{{ end }}

	{{ $style := resources.Get "css/main.css" | fingerprint }}
	<link rel="stylesheet" type="text/css" media="screen" href="{{ $style.Permalink }}" />

	{{- if or (eq .Site.Params.mode "auto") (eq .Site.Params.mode "dark") (eq .Site.Params.mode "toggle") -}}
		{{ $darkstyle := resources.Get "css/dark.css" | fingerprint }}
		<link id="darkModeStyle" rel="stylesheet" type="text/css" href="{{ $darkstyle.Permalink }}" {{ if eq .Site.Params.mode "auto" }}media="(prefers-color-scheme: dark)"{{ end }} {{ if eq .Site.Params.mode "toggle" }}disabled{{ end }} />
	{{- end -}}

	<!-- Mathjax support -->
	{{- if .Site.Params.mathjax | default false -}}
		<script type="text/javascript"
		{{ if .Site.Params.useCDN | default false -}}
		src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.1/MathJax.js?config=TeX-AMS-MML_HTMLorMML">
		{{- else -}}
		src="{{ absURL "js/MathJax.js" }}">
		{{- end -}}
		</script>
		<!-- inline Mathjax -->
		<script type="text/x-mathjax-config">
		MathJax.Hub.Config({
			tex2jax: {
				inlineMath: [['$','$'], ['\\(','\\)']],
				displayMath: [['$$','$$'], ['\[','\]']],
				processEscapes: true,
				processEnvironments: true,
				skipTags: ['script', 'noscript', 'style', 'textarea', 'pre'],
				TeX: { equationNumbers: { autoNumber: "AMS" },
						 extensions: ["AMSmath.js", "AMSsymbols.js"] }
			}
		});
		</script>
	{{- end -}}

	<!-- KaTeX support -->
	{{- if .Site.Params.katex | default false -}}
		{{ if .Site.Params.useCDN | default false -}}
		<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.15.2/dist/katex.min.css">
		<script defer src="https://cdn.jsdelivr.net/npm/katex@0.15.2/dist/katex.min.js"></script>
		<script defer src="https://cdn.jsdelivr.net/npm/katex@0.15.2/dist/contrib/auto-render.min.js" onload="renderMathInElement(document.body);"></script>
		{{- else -}}
		<link rel="stylesheet" href="{{ absURL "katex/katex.min.css" }} ">
		<script defer src="{{ absURL "katex/katex.min.js" }}"></script>
		<script defer src="{{ absURL "katex/auto-render.min.js" }}" onload="renderMathInElement(document.body);"></script>
		{{ end -}}
		<!-- inline KaTeX -->
		<script>
			document.addEventListener("DOMContentLoaded", function() {
					renderMathInElement(document.body, {
							delimiters: [
									{left: "$$", right: "$$", display: true},
									{left: "$", right: "$", display: false}
							]
					});
			});
		</script>
	{{- end -}}

	<!-- Custom CSS style get applied last -->
	{{- if isset .Site.Params "customcss" }}
		{{ range .Site.Params.customCSS }}
		{{ $customstyle := resources.Get . | fingerprint }}
		<link rel="stylesheet" type="text/css" href="{{ $customstyle.Permalink }}">
		{{ end }}
	{{- end -}}
	{{- range .Site.Params.customJS }}
	{{- if or (hasPrefix . "http://") (hasPrefix . "https://") }}
	<script src="{{ . }}"></script>
	{{- else if (hasPrefix . "<script")}}
    {{ .| safeHTML }}
	{{- else }}
	<script src="{{ absURL . }}"></script>
	{{- end }}
	{{- end }}
</head>
